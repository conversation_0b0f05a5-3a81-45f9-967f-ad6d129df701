"""
资源管理测试
测试临时文件清理、内存管理等资源相关功能
"""

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch

import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.tools.image_tools import ImageProcessor
from src.tools.ocr_tools import OCRProcessor


class TestResourceManagement:
    """资源管理测试类"""
    
    @pytest.fixture
    def temp_image_file(self):
        """创建临时图片文件"""
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as f:
            # 创建一个简单的测试文件
            f.write(b'fake image data')
            yield f.name
        
        # 清理
        if os.path.exists(f.name):
            os.unlink(f.name)
    
    def test_image_processor_temp_file_tracking(self):
        """测试图片处理器临时文件跟踪"""
        processor = ImageProcessor()
        
        # 初始状态应该没有临时文件
        assert len(processor.temp_files) == 0
        
        # 模拟添加临时文件
        processor.temp_files.append("test_temp_file.jpg")
        assert len(processor.temp_files) == 1
        
        # 测试清理
        processor.cleanup_temp_files()
        assert len(processor.temp_files) == 0
    
    def test_ocr_processor_temp_file_tracking(self):
        """测试OCR处理器临时文件跟踪"""
        processor = OCRProcessor()
        
        # 初始状态应该没有临时文件
        assert len(processor.temp_files) == 0
        
        # 模拟添加临时文件
        processor.temp_files.append("test_ocr_temp_file.jpg")
        assert len(processor.temp_files) == 1
        
        # 测试清理
        processor.cleanup_temp_files()
        assert len(processor.temp_files) == 0
    
    @pytest.mark.asyncio
    @patch('src.tools.image_tools.cv2.imread')
    @patch('src.tools.image_tools.Image.open')
    async def test_image_processor_cleanup_on_error(self, mock_image_open, mock_cv2_imread, temp_image_file):
        """测试图片处理器在错误时的清理"""
        processor = ImageProcessor()

        # 模拟图片加载失败
        mock_image_open.side_effect = Exception("图片加载失败")

        # 尝试增强图片，应该抛出异常
        with pytest.raises(Exception):
            await processor.enhance_image(temp_image_file)

        # 即使出错，临时文件列表也应该被正确管理
        assert isinstance(processor.temp_files, list)
    
    def test_image_processor_destructor_cleanup(self):
        """测试图片处理器析构函数清理"""
        processor = ImageProcessor()
        
        # 添加一些模拟的临时文件
        processor.temp_files = ["temp1.jpg", "temp2.jpg"]
        
        # 手动调用析构函数
        processor.__del__()
        
        # 临时文件列表应该被清空
        assert len(processor.temp_files) == 0
    
    def test_ocr_processor_destructor_cleanup(self):
        """测试OCR处理器析构函数清理"""
        processor = OCRProcessor()
        
        # 添加一些模拟的临时文件
        processor.temp_files = ["temp1_ocr.jpg", "temp2_ocr.jpg"]
        
        # 手动调用析构函数
        processor.__del__()
        
        # 临时文件列表应该被清空
        assert len(processor.temp_files) == 0
    
    def test_temp_file_cleanup_with_real_files(self):
        """测试真实文件的清理"""
        processor = ImageProcessor()
        
        # 创建真实的临时文件
        temp_files = []
        for i in range(3):
            with tempfile.NamedTemporaryFile(suffix=f'_test_{i}.jpg', delete=False) as f:
                f.write(b'test data')
                temp_files.append(f.name)
                processor.temp_files.append(f.name)
        
        # 确认文件存在
        for temp_file in temp_files:
            assert Path(temp_file).exists()
        
        # 清理临时文件
        processor.cleanup_temp_files()
        
        # 确认文件被删除
        for temp_file in temp_files:
            assert not Path(temp_file).exists()
        
        # 确认临时文件列表被清空
        assert len(processor.temp_files) == 0
    
    def test_cleanup_nonexistent_files(self):
        """测试清理不存在的文件"""
        processor = ImageProcessor()
        
        # 添加不存在的文件路径
        processor.temp_files = ["nonexistent1.jpg", "nonexistent2.jpg"]
        
        # 清理应该不会抛出异常
        processor.cleanup_temp_files()
        
        # 临时文件列表应该被清空
        assert len(processor.temp_files) == 0
    
    def test_multiple_processors_independence(self):
        """测试多个处理器实例的独立性"""
        processor1 = ImageProcessor()
        processor2 = ImageProcessor()
        
        # 为不同处理器添加不同的临时文件
        processor1.temp_files.append("temp1.jpg")
        processor2.temp_files.append("temp2.jpg")
        
        assert len(processor1.temp_files) == 1
        assert len(processor2.temp_files) == 1
        assert processor1.temp_files[0] != processor2.temp_files[0]
        
        # 清理一个处理器不应该影响另一个
        processor1.cleanup_temp_files()
        
        assert len(processor1.temp_files) == 0
        assert len(processor2.temp_files) == 1
        
        # 清理第二个处理器
        processor2.cleanup_temp_files()
        assert len(processor2.temp_files) == 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
