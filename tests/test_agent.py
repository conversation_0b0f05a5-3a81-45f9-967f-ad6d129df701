"""
Agent系统测试
"""

import pytest
import asyncio
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.agent import ImageLocationAgent
from src.core.context_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, TaskStatus
from src.actors.image_analysis_actor import ImageAnalysisActor
from src.utils import config


class TestImageLocationAgent:
    """Agent系统测试类"""
    
    @pytest.fixture
    def mock_llm_client(self):
        """模拟LLM客户端"""
        client = Mock()
        client.chat = Mock()
        client.chat.completions = Mock()
        client.chat.completions.create = AsyncMock()
        return client
    
    @pytest.fixture
    def agent(self, mock_llm_client):
        """创建测试Agent实例"""
        return ImageLocationAgent(mock_llm_client)
    
    @pytest.fixture
    def test_image_path(self):
        """创建测试图片文件"""
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as f:
            # 创建一个简单的测试文件
            f.write(b'fake image data')
            return f.name
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, mock_llm_client):
        """测试Agent初始化"""
        agent = ImageLocationAgent(mock_llm_client)
        
        assert agent.llm_client == mock_llm_client
        assert agent.planner is not None
        assert agent.context_manager is not None
        assert len(agent.actors) == 4
        assert "image_analysis" in agent.actors
        assert "search" in agent.actors
        assert "reflection" in agent.actors
        assert "conclusion" in agent.actors
    
    @pytest.mark.asyncio
    async def test_health_check(self, agent):
        """测试健康检查"""
        health_status = await agent.health_check()
        
        assert "agent_status" in health_status
        assert "llm_client_available" in health_status
        assert "actors_status" in health_status
        assert "configuration" in health_status
        
        assert health_status["agent_status"] == "healthy"
        assert health_status["llm_client_available"] is True
        assert len(health_status["actors_status"]) == 4
    
    @pytest.mark.asyncio
    async def test_context_management(self, agent, test_image_path):
        """测试上下文管理"""
        # 初始化上下文
        agent.context_manager.initialize_context(
            image_path=test_image_path,
            user_requirements="测试要求",
            image_description="测试图片"
        )
        
        # 检查上下文状态
        state = agent.context_manager.get_current_state()
        assert "context" in state
        assert state["context"]["image_path"] == test_image_path
        assert state["context"]["user_requirements"] == "测试要求"
        
        # 测试重置上下文
        agent.reset_context()
        state = agent.context_manager.get_current_state()
        assert not state  # 应该为空
    
    @pytest.mark.asyncio
    async def test_actor_stats(self, agent):
        """测试Actor统计信息"""
        stats = agent.get_actor_stats()
        
        assert len(stats) == 4
        for actor_name in ["image_analysis", "search", "reflection", "conclusion"]:
            assert actor_name in stats
            assert "actor_type" in stats[actor_name]
            assert "execution_count" in stats[actor_name]
    
    @pytest.mark.asyncio
    @patch('src.core.agent.ImageLocationAgent._generate_basic_image_description')
    @patch('src.core.planner.Planner.create_plan')
    async def test_analyze_image_location_basic(
        self, 
        mock_create_plan,
        mock_generate_desc,
        agent, 
        test_image_path
    ):
        """测试基本的图片位置分析流程"""
        
        # 模拟方法返回值
        mock_generate_desc.return_value = "测试图片描述"
        
        # 模拟执行计划
        from src.core.planner import ExecutionPlan, PlanStep, PlanStage
        mock_plan = ExecutionPlan(
            plan_id="test_plan",
            steps=[],
            estimated_duration=60.0,
            success_criteria={},
            fallback_strategies=[]
        )
        mock_create_plan.return_value = mock_plan
        
        # 模拟Actor执行结果
        with patch.object(agent, '_execute_analysis_workflow') as mock_workflow:
            mock_workflow.return_value = {
                "final_location": {"specific_location": "测试位置"},
                "confidence_score": 0.8,
                "reasoning_process": "测试推理过程",
                "key_evidence": ["测试证据"],
                "uncertainty_factors": [],
                "alternative_possibilities": []
            }
            
            # 执行分析
            result = await agent.analyze_image_location(
                image_path=test_image_path,
                user_requirements="识别位置"
            )
            
            # 验证结果
            assert result["success"] is True
            assert "final_location" in result
            assert "confidence_score" in result
            assert result["confidence_score"] == 0.8
    
    @pytest.mark.asyncio
    async def test_analyze_image_location_error_handling(self, agent):
        """测试错误处理"""
        # 使用不存在的图片路径
        result = await agent.analyze_image_location(
            image_path="nonexistent.jpg",
            user_requirements="测试"
        )
        
        # 应该返回错误结果
        assert result["success"] is False
        assert "error" in result
        assert result["confidence_score"] == 0.0


class TestContextManager:
    """上下文管理器测试"""
    
    @pytest.fixture
    def context_manager(self):
        """创建测试上下文管理器"""
        return ContextManager()
    
    def test_initialization(self, context_manager):
        """测试初始化"""
        assert context_manager.current_context is None
        assert len(context_manager.task_history) == 0
        assert context_manager.session_id is not None
    
    def test_initialize_context(self, context_manager):
        """测试上下文初始化"""
        context_manager.initialize_context(
            image_path="test.jpg",
            user_requirements="测试要求",
            image_description="测试描述"
        )
        
        assert context_manager.current_context is not None
        assert context_manager.current_context.image_path == "test.jpg"
        assert context_manager.current_context.user_requirements == "测试要求"
        assert context_manager.current_context.iteration_count == 0
    
    def test_add_task_result(self, context_manager):
        """测试添加任务结果"""
        from src.core.context_manager import TaskResult, TaskStatus
        from datetime import datetime
        
        # 初始化上下文
        context_manager.initialize_context("test.jpg", "测试", "描述")
        
        # 创建任务结果
        task_result = TaskResult(
            task_id="test_task",
            actor_type="image_analysis",
            status=TaskStatus.COMPLETED,
            result={"test": "data"},
            confidence=0.8,
            timestamp=datetime.now(),
            execution_time=1.0
        )
        
        # 添加任务结果
        context_manager.add_task_result(task_result)
        
        assert len(context_manager.task_history) == 1
        assert context_manager.current_context.extracted_clues == {"test": "data"}
        assert len(context_manager.current_context.confidence_history) == 1
    
    def test_should_continue_analysis(self, context_manager):
        """测试是否应该继续分析"""
        # 未初始化上下文
        assert not context_manager.should_continue_analysis(5, 0.8)
        
        # 初始化上下文
        context_manager.initialize_context("test.jpg", "测试", "描述")
        
        # 应该继续（未达到最大迭代次数和置信度）
        assert context_manager.should_continue_analysis(5, 0.8)
        
        # 达到最大迭代次数
        context_manager.current_context.iteration_count = 5
        assert not context_manager.should_continue_analysis(5, 0.8)
        
        # 重置并测试置信度
        context_manager.current_context.iteration_count = 0
        context_manager.current_context.confidence_history = [0.9]
        assert not context_manager.should_continue_analysis(5, 0.8)
    
    def test_get_analysis_summary(self, context_manager):
        """测试获取分析摘要"""
        from src.core.context_manager import TaskResult, TaskStatus
        from datetime import datetime
        
        # 初始化上下文
        context_manager.initialize_context("test.jpg", "测试", "描述")
        
        # 添加一些任务结果
        for i in range(3):
            task_result = TaskResult(
                task_id=f"task_{i}",
                actor_type="test_actor",
                status=TaskStatus.COMPLETED,
                result={},
                confidence=0.5,
                timestamp=datetime.now(),
                execution_time=1.0
            )
            context_manager.add_task_result(task_result)
        
        # 获取摘要
        summary = context_manager.get_analysis_summary()
        
        assert summary["total_tasks"] == 3
        assert summary["successful_tasks"] == 3
        assert summary["failed_tasks"] == 0
        assert "execution_time" in summary


class TestImageAnalysisActor:
    """图片分析Actor测试"""
    
    @pytest.fixture
    def mock_llm_client(self):
        """模拟LLM客户端"""
        client = Mock()
        client.chat = Mock()
        client.chat.completions = Mock()
        client.chat.completions.create = AsyncMock()
        return client
    
    @pytest.fixture
    def actor(self, mock_llm_client):
        """创建测试Actor"""
        return ImageAnalysisActor(mock_llm_client)
    
    @pytest.fixture
    def test_image_path(self):
        """创建测试图片文件"""
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as f:
            f.write(b'fake image data')
            return f.name
    
    def test_actor_initialization(self, actor):
        """测试Actor初始化"""
        assert actor.actor_type == "image_analysis"
        assert actor.llm_client is not None
        assert actor.image_processor is not None
        assert actor.ocr_processor is not None
    
    @pytest.mark.asyncio
    async def test_execute_missing_input(self, actor):
        """测试缺少输入参数的情况"""
        result = await actor.execute({})
        
        assert result.status == TaskStatus.FAILED
        assert "缺少必需的输入参数" in result.error_message
    
    @pytest.mark.asyncio
    @patch('src.actors.image_analysis_actor.ImageAnalysisActor._preprocess_image')
    @patch('src.actors.image_analysis_actor.ImageAnalysisActor._extract_text_clues')
    @patch('src.actors.image_analysis_actor.ImageAnalysisActor._analyze_visual_features')
    @patch('src.actors.image_analysis_actor.ImageAnalysisActor._identify_landmarks')
    @patch('src.actors.image_analysis_actor.ImageAnalysisActor._analyze_environment')
    async def test_execute_success(
        self,
        mock_analyze_env,
        mock_identify_landmarks,
        mock_analyze_visual,
        mock_extract_text,
        mock_preprocess,
        actor,
        test_image_path
    ):
        """测试成功执行"""
        
        # 模拟各个步骤的返回值
        mock_preprocess.return_value = test_image_path
        mock_extract_text.return_value = {
            "extracted_texts": ["测试文字"],
            "geographic_texts": ["北京"]
        }
        mock_analyze_visual.return_value = {
            "architectural_features": ["现代建筑"],
            "cultural_features": ["中式风格"]
        }
        mock_identify_landmarks.return_value = {
            "landmarks": [{"name": "测试地标"}],
            "buildings": []
        }
        mock_analyze_env.return_value = {
            "vegetation": "urban",
            "climate": "temperate"
        }
        
        # 执行测试
        task_input = {"image_path": test_image_path}
        result = await actor.execute(task_input)
        
        # 验证结果
        assert result.status == TaskStatus.COMPLETED
        assert "text_clues" in result.result
        assert "architectural_features" in result.result
        assert result.confidence > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
