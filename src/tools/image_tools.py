"""
图片处理工具模块
提供图片增强、目标检测、特征分析等功能
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance
from typing import Dict, Any, List, Tuple
import asyncio
from pathlib import Path

from ..utils import app_logger, config


class ImageProcessor:
    """图片处理器"""
    
    def __init__(self):
        self.logger = app_logger
        self.max_size = config.tools.max_image_size
        self.supported_formats = config.tools.supported_formats
        self.temp_files = []  # 跟踪临时文件
    
    async def enhance_image(self, image_path: str) -> str:
        """增强图片质量"""
        try:
            self.logger.info(f"开始增强图片: {image_path}")
            
            # 检查文件格式
            if not self._is_supported_format(image_path):
                raise ValueError(f"不支持的图片格式: {image_path}")
            
            # 加载图片
            image = Image.open(image_path)
            
            # 调整图片大小
            image = self._resize_image(image)
            
            # 增强图片
            enhanced_image = self._apply_enhancements(image)
            
            # 保存增强后的图片
            enhanced_path = self._get_enhanced_path(image_path)
            enhanced_image.save(enhanced_path, quality=95)

            # 记录临时文件
            self.temp_files.append(enhanced_path)

            self.logger.info(f"图片增强完成: {enhanced_path}")
            return enhanced_path
            
        except Exception as e:
            self.logger.error(f"图片增强失败: {str(e)}")
            raise
    
    async def detect_objects(self, image_path: str) -> Dict[str, Any]:
        """检测图片中的对象"""
        try:
            self.logger.info(f"开始目标检测: {image_path}")
            
            # 加载图片
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法加载图片: {image_path}")
            
            # 使用OpenCV进行基础目标检测
            # 这里实现一个简化的目标检测逻辑
            objects = await self._detect_basic_objects(image)
            
            result = {
                "objects": objects,
                "image_size": image.shape[:2],
                "detection_method": "opencv_basic"
            }
            
            self.logger.info(f"目标检测完成，发现 {len(objects)} 个对象")
            return result
            
        except Exception as e:
            self.logger.error(f"目标检测失败: {str(e)}")
            return {"objects": [], "error": str(e)}
    
    async def analyze_environment(self, image_path: str) -> Dict[str, Any]:
        """分析图片的环境特征"""
        try:
            self.logger.info(f"开始环境分析: {image_path}")
            
            # 加载图片
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法加载图片: {image_path}")
            
            # 分析颜色分布
            color_analysis = self._analyze_color_distribution(image)
            
            # 分析纹理特征
            texture_analysis = self._analyze_texture_features(image)
            
            # 分析亮度和对比度
            lighting_analysis = self._analyze_lighting(image)
            
            # 推断环境特征
            environment_features = self._infer_environment_features(
                color_analysis, texture_analysis, lighting_analysis
            )
            
            self.logger.info("环境分析完成")
            return environment_features
            
        except Exception as e:
            self.logger.error(f"环境分析失败: {str(e)}")
            return {"error": str(e)}
    
    async def extract_regions_of_interest(self, image_path: str) -> List[Dict[str, Any]]:
        """提取感兴趣的区域"""
        try:
            self.logger.info(f"开始提取感兴趣区域: {image_path}")
            
            # 加载图片
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法加载图片: {image_path}")
            
            # 使用边缘检测找到感兴趣的区域
            regions = self._find_interesting_regions(image)
            
            self.logger.info(f"提取到 {len(regions)} 个感兴趣区域")
            return regions
            
        except Exception as e:
            self.logger.error(f"区域提取失败: {str(e)}")
            return []
    
    def _is_supported_format(self, image_path: str) -> bool:
        """检查是否为支持的图片格式"""
        file_ext = Path(image_path).suffix.lower().lstrip('.')
        return file_ext in self.supported_formats
    
    def _resize_image(self, image: Image.Image) -> Image.Image:
        """调整图片大小"""
        width, height = image.size
        
        if max(width, height) > self.max_size:
            if width > height:
                new_width = self.max_size
                new_height = int(height * (self.max_size / width))
            else:
                new_height = self.max_size
                new_width = int(width * (self.max_size / height))
            
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        return image
    
    def _apply_enhancements(self, image: Image.Image) -> Image.Image:
        """应用图片增强"""
        if not config.tools.enhancement_enabled:
            return image
        
        # 增强对比度
        contrast_enhancer = ImageEnhance.Contrast(image)
        image = contrast_enhancer.enhance(1.2)
        
        # 增强锐度
        sharpness_enhancer = ImageEnhance.Sharpness(image)
        image = sharpness_enhancer.enhance(1.1)
        
        # 增强颜色饱和度
        color_enhancer = ImageEnhance.Color(image)
        image = color_enhancer.enhance(1.1)
        
        return image
    
    def _get_enhanced_path(self, original_path: str) -> str:
        """获取增强图片的保存路径"""
        path = Path(original_path)
        enhanced_name = f"{path.stem}_enhanced{path.suffix}"
        return str(path.parent / enhanced_name)

    def cleanup_temp_files(self):
        """清理临时文件"""
        for temp_file in self.temp_files:
            try:
                if Path(temp_file).exists():
                    Path(temp_file).unlink()
                    self.logger.info(f"已清理临时文件: {temp_file}")
            except Exception as e:
                self.logger.warning(f"清理临时文件失败 {temp_file}: {str(e)}")

        self.temp_files.clear()

    def __del__(self):
        """析构函数，确保临时文件被清理"""
        try:
            self.cleanup_temp_files()
        except:
            pass  # 忽略析构函数中的异常
    
    async def _detect_basic_objects(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """基础目标检测"""
        objects = []
        
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 使用Canny边缘检测
        edges = cv2.Canny(gray, 50, 150)
        
        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 筛选有意义的轮廓
        for i, contour in enumerate(contours):
            area = cv2.contourArea(contour)
            if area > 1000:  # 过滤小的轮廓
                x, y, w, h = cv2.boundingRect(contour)
                
                # 简单的形状分类
                object_class = self._classify_shape(contour, area)
                
                objects.append({
                    "id": i,
                    "class": object_class,
                    "bbox": [x, y, w, h],
                    "area": area,
                    "confidence": min(area / 10000, 1.0)  # 简单的置信度计算
                })
        
        return objects
    
    def _classify_shape(self, contour: np.ndarray, area: float) -> str:
        """简单的形状分类"""
        # 计算轮廓的近似多边形
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # 根据顶点数量进行简单分类
        vertices = len(approx)
        
        if vertices == 3:
            return "triangle"
        elif vertices == 4:
            # 检查是否为矩形
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h
            if 0.8 <= aspect_ratio <= 1.2:
                return "square"
            else:
                return "rectangle"
        elif vertices > 8:
            return "circle"
        else:
            return "polygon"
    
    def _analyze_color_distribution(self, image: np.ndarray) -> Dict[str, Any]:
        """分析颜色分布"""
        # 转换到HSV颜色空间
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # 计算颜色直方图
        hist_h = cv2.calcHist([hsv], [0], None, [180], [0, 180])
        hist_s = cv2.calcHist([hsv], [1], None, [256], [0, 256])
        hist_v = cv2.calcHist([hsv], [2], None, [256], [0, 256])
        
        # 分析主要颜色
        dominant_hue = np.argmax(hist_h)
        dominant_saturation = np.argmax(hist_s)
        dominant_value = np.argmax(hist_v)
        
        return {
            "dominant_hue": int(dominant_hue),
            "dominant_saturation": int(dominant_saturation),
            "dominant_value": int(dominant_value),
            "color_variety": float(np.std(hist_h))
        }
    
    def _analyze_texture_features(self, image: np.ndarray) -> Dict[str, Any]:
        """分析纹理特征"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 计算局部二值模式 (简化版)
        texture_variance = float(np.var(gray))
        
        # 计算边缘密度
        edges = cv2.Canny(gray, 50, 150)
        edge_density = float(np.sum(edges > 0) / edges.size)
        
        return {
            "texture_variance": texture_variance,
            "edge_density": edge_density,
            "smoothness": 1.0 / (1.0 + texture_variance / 1000)
        }
    
    def _analyze_lighting(self, image: np.ndarray) -> Dict[str, Any]:
        """分析光照条件"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 计算亮度统计
        mean_brightness = float(np.mean(gray))
        brightness_std = float(np.std(gray))
        
        # 判断光照条件
        if mean_brightness < 80:
            lighting_condition = "dark"
        elif mean_brightness > 180:
            lighting_condition = "bright"
        else:
            lighting_condition = "normal"
        
        return {
            "mean_brightness": mean_brightness,
            "brightness_variance": brightness_std,
            "lighting_condition": lighting_condition,
            "contrast": brightness_std / mean_brightness if mean_brightness > 0 else 0
        }
    
    def _infer_environment_features(
        self,
        color_analysis: Dict[str, Any],
        texture_analysis: Dict[str, Any],
        lighting_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """推断环境特征"""
        
        # 基于颜色推断植被类型
        dominant_hue = color_analysis["dominant_hue"]
        if 35 <= dominant_hue <= 85:  # 绿色范围
            vegetation = "abundant"
        elif 15 <= dominant_hue <= 35:  # 黄绿色
            vegetation = "moderate"
        else:
            vegetation = "sparse"
        
        # 基于纹理推断地形
        edge_density = texture_analysis["edge_density"]
        if edge_density > 0.1:
            terrain = "urban"
        elif edge_density > 0.05:
            terrain = "suburban"
        else:
            terrain = "rural"
        
        # 基于亮度推断天气
        lighting_condition = lighting_analysis["lighting_condition"]
        weather_map = {
            "bright": "sunny",
            "normal": "partly_cloudy",
            "dark": "cloudy"
        }
        weather = weather_map.get(lighting_condition, "unknown")
        
        return {
            "vegetation": vegetation,
            "terrain": terrain,
            "weather": weather,
            "lighting": lighting_condition,
            "climate": self._infer_climate(color_analysis, lighting_analysis)
        }
    
    def _infer_climate(self, color_analysis: Dict[str, Any], lighting_analysis: Dict[str, Any]) -> str:
        """推断气候类型"""
        brightness = lighting_analysis["mean_brightness"]
        color_variety = color_analysis["color_variety"]
        
        if brightness > 150 and color_variety < 50:
            return "arid"
        elif brightness > 120 and color_variety > 80:
            return "tropical"
        elif brightness < 100:
            return "temperate"
        else:
            return "unknown"
    
    def _find_interesting_regions(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """查找感兴趣的区域"""
        regions = []
        
        # 使用SIFT特征检测器（如果可用）
        try:
            sift = cv2.SIFT_create()
            keypoints = sift.detect(image, None)
            
            # 将关键点转换为感兴趣区域
            for i, kp in enumerate(keypoints[:20]):  # 限制数量
                x, y = int(kp.pt[0]), int(kp.pt[1])
                size = int(kp.size)
                
                regions.append({
                    "id": i,
                    "center": [x, y],
                    "size": size,
                    "response": float(kp.response),
                    "type": "keypoint"
                })
        
        except Exception as e:
            self.logger.warning(f"SIFT特征检测失败: {str(e)}")
            # 使用简单的角点检测作为备选
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            corners = cv2.goodFeaturesToTrack(gray, 20, 0.01, 10)
            
            if corners is not None:
                for i, corner in enumerate(corners):
                    x, y = int(corner[0][0]), int(corner[0][1])
                    regions.append({
                        "id": i,
                        "center": [x, y],
                        "size": 10,
                        "response": 1.0,
                        "type": "corner"
                    })
        
        return regions
