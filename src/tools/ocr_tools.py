"""
OCR工具模块
提供文字识别和文本处理功能
"""

import pytesseract
import easyocr
import cv2
import numpy as np
from typing import Dict, Any, List, Tuple
from PIL import Image
import asyncio
import re
from pathlib import Path

from ..utils import app_logger, config


class OCRProcessor:
    """OCR处理器"""
    
    def __init__(self):
        self.logger = app_logger
        self.engines = config.tools.ocr_engines
        self.languages = config.tools.ocr_languages
        self.confidence_threshold = config.tools.ocr_confidence_threshold
        self.temp_files = []  # 跟踪临时文件
        
        # 初始化EasyOCR
        if "easyocr" in self.engines:
            try:
                # EasyOCR支持的语言代码映射
                easyocr_languages = []
                for lang in self.languages:
                    if lang == "eng":
                        easyocr_languages.append("en")
                    elif lang == "chi_sim":
                        easyocr_languages.append("ch_sim")
                    elif lang == "chi_tra":
                        easyocr_languages.append("ch_tra")
                    elif lang == "jpn":
                        easyocr_languages.append("ja")
                    elif lang == "kor":
                        easyocr_languages.append("ko")

                # 如果没有支持的语言，默认使用英文
                if not easyocr_languages:
                    easyocr_languages = ["en"]

                self.easyocr_reader = easyocr.Reader(easyocr_languages)
            except Exception as e:
                self.logger.warning(f"EasyOCR初始化失败: {str(e)}")
                self.easyocr_reader = None
        else:
            self.easyocr_reader = None
    
    async def extract_text(self, image_path: str) -> Dict[str, Any]:
        """提取图片中的文字"""
        try:
            self.logger.info(f"开始OCR文字提取: {image_path}")
            
            # 预处理图片
            processed_image = await self._preprocess_for_ocr(image_path)
            
            # 使用多个OCR引擎
            results = {}
            
            if "tesseract" in self.engines:
                tesseract_result = await self._extract_with_tesseract(processed_image)
                results["tesseract"] = tesseract_result
            
            if "easyocr" in self.engines and self.easyocr_reader:
                easyocr_result = await self._extract_with_easyocr(processed_image)
                results["easyocr"] = easyocr_result
            
            # 合并和优化结果
            final_result = self._merge_ocr_results(results)
            
            self.logger.info(f"OCR完成，提取到 {len(final_result.get('texts', []))} 个文本")
            return final_result
            
        except Exception as e:
            self.logger.error(f"OCR文字提取失败: {str(e)}")
            return {"texts": [], "error": str(e)}
    
    async def extract_text_with_regions(self, image_path: str) -> Dict[str, Any]:
        """提取文字并返回位置信息"""
        try:
            self.logger.info(f"开始带位置的OCR提取: {image_path}")
            
            # 预处理图片
            processed_image = await self._preprocess_for_ocr(image_path)
            
            # 使用Tesseract获取详细位置信息
            detailed_result = await self._extract_with_positions(processed_image)
            
            return detailed_result
            
        except Exception as e:
            self.logger.error(f"带位置OCR提取失败: {str(e)}")
            return {"texts": [], "positions": [], "error": str(e)}
    
    async def detect_languages(self, image_path: str) -> List[str]:
        """检测图片中的语言"""
        try:
            self.logger.info(f"开始语言检测: {image_path}")
            
            # 使用EasyOCR进行语言检测
            if self.easyocr_reader:
                results = self.easyocr_reader.readtext(image_path)
                
                # 分析检测到的文字，推断语言
                detected_languages = set()
                for (bbox, text, confidence) in results:
                    if confidence > self.confidence_threshold:
                        lang = self._detect_text_language(text)
                        if lang:
                            detected_languages.add(lang)
                
                return list(detected_languages)
            else:
                return ["eng"]  # 默认英语
                
        except Exception as e:
            self.logger.error(f"语言检测失败: {str(e)}")
            return ["eng"]
    
    async def _preprocess_for_ocr(self, image_path: str) -> str:
        """为OCR预处理图片"""
        try:
            # 加载图片
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法加载图片: {image_path}")
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 去噪
            denoised = cv2.medianBlur(gray, 3)
            
            # 增强对比度
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(denoised)
            
            # 二值化
            _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # 保存预处理后的图片
            processed_path = image_path.replace('.', '_ocr_processed.')
            cv2.imwrite(processed_path, binary)

            # 记录临时文件
            self.temp_files.append(processed_path)

            return processed_path
            
        except Exception as e:
            self.logger.warning(f"OCR预处理失败，使用原图: {str(e)}")
            return image_path
    
    async def _extract_with_tesseract(self, image_path: str) -> Dict[str, Any]:
        """使用Tesseract提取文字"""
        try:
            # 配置Tesseract
            config_str = '--oem 3 --psm 6'
            
            # 提取文字
            text = pytesseract.image_to_string(
                Image.open(image_path),
                config=config_str,
                lang='+'.join(self.languages)
            )
            
            # 获取详细信息
            data = pytesseract.image_to_data(
                Image.open(image_path),
                config=config_str,
                lang='+'.join(self.languages),
                output_type=pytesseract.Output.DICT
            )
            
            # 过滤低置信度的结果
            texts = []
            confidences = []
            locations = []
            
            for i in range(len(data['text'])):
                if int(data['conf'][i]) > self.confidence_threshold * 100:
                    text_item = data['text'][i].strip()
                    if text_item:
                        texts.append(text_item)
                        confidences.append(float(data['conf'][i]) / 100)
                        locations.append({
                            'x': data['left'][i],
                            'y': data['top'][i],
                            'width': data['width'][i],
                            'height': data['height'][i]
                        })
            
            return {
                "texts": texts,
                "confidences": confidences,
                "locations": locations,
                "full_text": text.strip(),
                "engine": "tesseract"
            }
            
        except Exception as e:
            self.logger.error(f"Tesseract提取失败: {str(e)}")
            return {"texts": [], "error": str(e)}
    
    async def _extract_with_easyocr(self, image_path: str) -> Dict[str, Any]:
        """使用EasyOCR提取文字"""
        try:
            if not self.easyocr_reader:
                return {"texts": [], "error": "EasyOCR未初始化"}
            
            # 提取文字
            results = self.easyocr_reader.readtext(image_path)
            
            texts = []
            confidences = []
            locations = []
            
            for (bbox, text, confidence) in results:
                if confidence > self.confidence_threshold:
                    texts.append(text)
                    confidences.append(confidence)
                    
                    # 转换边界框格式
                    x_coords = [point[0] for point in bbox]
                    y_coords = [point[1] for point in bbox]
                    
                    locations.append({
                        'x': min(x_coords),
                        'y': min(y_coords),
                        'width': max(x_coords) - min(x_coords),
                        'height': max(y_coords) - min(y_coords)
                    })
            
            return {
                "texts": texts,
                "confidences": confidences,
                "locations": locations,
                "full_text": " ".join(texts),
                "engine": "easyocr"
            }
            
        except Exception as e:
            self.logger.error(f"EasyOCR提取失败: {str(e)}")
            return {"texts": [], "error": str(e)}
    
    async def _extract_with_positions(self, image_path: str) -> Dict[str, Any]:
        """提取文字并返回详细位置信息"""
        try:
            # 使用Tesseract获取详细位置信息
            data = pytesseract.image_to_data(
                Image.open(image_path),
                output_type=pytesseract.Output.DICT,
                lang='+'.join(self.languages)
            )
            
            # 组织结果
            text_regions = []
            
            for i in range(len(data['text'])):
                if int(data['conf'][i]) > self.confidence_threshold * 100:
                    text_item = data['text'][i].strip()
                    if text_item:
                        text_regions.append({
                            'text': text_item,
                            'confidence': float(data['conf'][i]) / 100,
                            'bbox': {
                                'x': data['left'][i],
                                'y': data['top'][i],
                                'width': data['width'][i],
                                'height': data['height'][i]
                            },
                            'level': data['level'][i],
                            'page_num': data['page_num'][i],
                            'block_num': data['block_num'][i],
                            'par_num': data['par_num'][i],
                            'line_num': data['line_num'][i],
                            'word_num': data['word_num'][i]
                        })
            
            return {
                "text_regions": text_regions,
                "total_regions": len(text_regions),
                "engine": "tesseract_detailed"
            }
            
        except Exception as e:
            self.logger.error(f"详细位置提取失败: {str(e)}")
            return {"text_regions": [], "error": str(e)}
    
    def _merge_ocr_results(self, results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """合并多个OCR引擎的结果"""
        merged_texts = []
        merged_confidences = []
        merged_locations = []
        detected_languages = []
        
        # 收集所有文本
        all_texts = {}
        
        for engine, result in results.items():
            if "error" not in result:
                texts = result.get("texts", [])
                confidences = result.get("confidences", [])
                locations = result.get("locations", [])
                
                for i, text in enumerate(texts):
                    # 清理文本
                    cleaned_text = self._clean_text(text)
                    if cleaned_text:
                        if cleaned_text not in all_texts:
                            all_texts[cleaned_text] = {
                                "text": cleaned_text,
                                "confidences": [],
                                "locations": [],
                                "engines": []
                            }
                        
                        all_texts[cleaned_text]["confidences"].append(
                            confidences[i] if i < len(confidences) else 0.5
                        )
                        all_texts[cleaned_text]["locations"].append(
                            locations[i] if i < len(locations) else {}
                        )
                        all_texts[cleaned_text]["engines"].append(engine)
        
        # 计算最终结果
        for text_data in all_texts.values():
            # 使用最高置信度
            max_confidence = max(text_data["confidences"])
            
            # 使用第一个位置信息
            location = text_data["locations"][0] if text_data["locations"] else {}
            
            merged_texts.append(text_data["text"])
            merged_confidences.append(max_confidence)
            merged_locations.append(location)
            
            # 检测语言
            lang = self._detect_text_language(text_data["text"])
            if lang and lang not in detected_languages:
                detected_languages.append(lang)
        
        return {
            "texts": merged_texts,
            "confidences": merged_confidences,
            "locations": merged_locations,
            "languages": detected_languages,
            "total_texts": len(merged_texts),
            "engines_used": list(results.keys())
        }
    
    def _clean_text(self, text: str) -> str:
        """清理提取的文本"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        cleaned = re.sub(r'\s+', ' ', text.strip())
        
        # 移除特殊字符（保留基本标点）
        cleaned = re.sub(r'[^\w\s\-.,!?()[\]{}:;"\'/\\]', '', cleaned)
        
        # 过滤太短的文本
        if len(cleaned) < 2:
            return ""
        
        return cleaned
    
    def _detect_text_language(self, text: str) -> str:
        """检测文本语言"""
        if not text:
            return ""
        
        # 简单的语言检测逻辑
        # 检测中文字符
        if re.search(r'[\u4e00-\u9fff]', text):
            return "chi_sim"
        
        # 检测日文字符
        if re.search(r'[\u3040-\u309f\u30a0-\u30ff]', text):
            return "jpn"
        
        # 检测韩文字符
        if re.search(r'[\uac00-\ud7af]', text):
            return "kor"
        
        # 默认为英文
        return "eng"

    def cleanup_temp_files(self):
        """清理临时文件"""
        for temp_file in self.temp_files:
            try:
                if Path(temp_file).exists():
                    Path(temp_file).unlink()
                    self.logger.info(f"已清理OCR临时文件: {temp_file}")
            except Exception as e:
                self.logger.warning(f"清理OCR临时文件失败 {temp_file}: {str(e)}")

        self.temp_files.clear()

    def __del__(self):
        """析构函数，确保临时文件被清理"""
        try:
            self.cleanup_temp_files()
        except:
            pass  # 忽略析构函数中的异常
