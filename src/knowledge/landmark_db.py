"""
地标数据库模块
管理世界著名地标的信息和识别
"""

import json
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import math

from ..utils import app_logger


class LandmarkDatabase:
    """地标数据库"""
    
    def __init__(self, db_path: str = "data/landmarks.json"):
        self.db_path = db_path
        self.logger = app_logger
        self.landmarks = {}
        self._load_database()
    
    def _load_database(self) -> None:
        """加载地标数据库"""
        try:
            db_file = Path(self.db_path)
            if db_file.exists():
                with open(db_file, 'r', encoding='utf-8') as f:
                    self.landmarks = json.load(f)
                self.logger.info(f"地标数据库加载完成: {len(self.landmarks)} 个地标")
            else:
                self.logger.warning(f"地标数据库文件不存在: {self.db_path}")
                self._create_default_database()
        except Exception as e:
            self.logger.error(f"加载地标数据库失败: {str(e)}")
            self._create_default_database()
    
    def _create_default_database(self) -> None:
        """创建默认地标数据库"""
        self.landmarks = {
            "eiffel_tower": {
                "name": "埃菲尔铁塔",
                "name_en": "Eiffel Tower",
                "location": {
                    "country": "法国",
                    "city": "巴黎",
                    "latitude": 48.8584,
                    "longitude": 2.2945
                },
                "category": "monument",
                "description": "法国巴黎的标志性建筑，铁制塔状结构",
                "keywords": ["铁塔", "巴黎", "法国", "埃菲尔", "tower"],
                "visual_features": ["铁制结构", "塔状", "格子状", "高耸"],
                "height": 330,
                "built_year": 1889
            },
            "great_wall": {
                "name": "长城",
                "name_en": "Great Wall of China",
                "location": {
                    "country": "中国",
                    "city": "北京等",
                    "latitude": 40.4319,
                    "longitude": 116.5704
                },
                "category": "historical",
                "description": "中国古代的军事防御工程",
                "keywords": ["长城", "万里长城", "中国", "城墙", "wall"],
                "visual_features": ["石制城墙", "蜿蜒", "山脊", "烽火台"],
                "length": 21196,
                "built_year": -220
            },
            "statue_of_liberty": {
                "name": "自由女神像",
                "name_en": "Statue of Liberty",
                "location": {
                    "country": "美国",
                    "city": "纽约",
                    "latitude": 40.6892,
                    "longitude": -74.0445
                },
                "category": "monument",
                "description": "美国纽约的标志性雕像",
                "keywords": ["自由女神", "纽约", "美国", "雕像", "liberty"],
                "visual_features": ["铜制雕像", "火炬", "王冠", "长袍"],
                "height": 93,
                "built_year": 1886
            },
            "big_ben": {
                "name": "大本钟",
                "name_en": "Big Ben",
                "location": {
                    "country": "英国",
                    "city": "伦敦",
                    "latitude": 51.5007,
                    "longitude": -0.1246
                },
                "category": "historical",
                "description": "英国伦敦威斯敏斯特宫的钟楼",
                "keywords": ["大本钟", "伦敦", "英国", "钟楼", "ben"],
                "visual_features": ["哥特式", "钟楼", "石制", "尖顶"],
                "height": 96,
                "built_year": 1859
            },
            "sydney_opera": {
                "name": "悉尼歌剧院",
                "name_en": "Sydney Opera House",
                "location": {
                    "country": "澳大利亚",
                    "city": "悉尼",
                    "latitude": -33.8568,
                    "longitude": 151.2153
                },
                "category": "modern",
                "description": "澳大利亚悉尼的标志性建筑",
                "keywords": ["悉尼歌剧院", "悉尼", "澳大利亚", "歌剧院", "opera"],
                "visual_features": ["贝壳状", "白色", "现代建筑", "海边"],
                "built_year": 1973
            },
            "taj_mahal": {
                "name": "泰姬陵",
                "name_en": "Taj Mahal",
                "location": {
                    "country": "印度",
                    "city": "阿格拉",
                    "latitude": 27.1751,
                    "longitude": 78.0421
                },
                "category": "historical",
                "description": "印度阿格拉的伊斯兰建筑杰作",
                "keywords": ["泰姬陵", "印度", "阿格拉", "陵墓", "taj"],
                "visual_features": ["白色大理石", "圆顶", "尖塔", "对称"],
                "built_year": 1653
            },
            "christ_redeemer": {
                "name": "基督救世主雕像",
                "name_en": "Christ the Redeemer",
                "location": {
                    "country": "巴西",
                    "city": "里约热内卢",
                    "latitude": -22.9519,
                    "longitude": -43.2105
                },
                "category": "religious",
                "description": "巴西里约热内卢的基督雕像",
                "keywords": ["基督", "里约", "巴西", "雕像", "christ"],
                "visual_features": ["巨大雕像", "张开双臂", "山顶", "白色"],
                "height": 38,
                "built_year": 1931
            },
            "machu_picchu": {
                "name": "马丘比丘",
                "name_en": "Machu Picchu",
                "location": {
                    "country": "秘鲁",
                    "city": "库斯科",
                    "latitude": -13.1631,
                    "longitude": -72.5450
                },
                "category": "historical",
                "description": "秘鲁的古印加遗址",
                "keywords": ["马丘比丘", "秘鲁", "印加", "遗址", "machu"],
                "visual_features": ["石制建筑", "梯田", "山顶", "古迹"],
                "built_year": 1450
            }
        }
        
        # 保存默认数据库
        self._save_database()
    
    def _save_database(self) -> None:
        """保存数据库到文件"""
        try:
            db_file = Path(self.db_path)
            db_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(db_file, 'w', encoding='utf-8') as f:
                json.dump(self.landmarks, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"地标数据库已保存: {self.db_path}")
        except Exception as e:
            self.logger.error(f"保存地标数据库失败: {str(e)}")
    
    def search_landmarks_by_keywords(self, keywords: List[str]) -> List[Dict[str, Any]]:
        """根据关键词搜索地标"""
        matching_landmarks = []
        
        try:
            for landmark_id, landmark_data in self.landmarks.items():
                score = 0
                landmark_keywords = landmark_data.get("keywords", [])
                landmark_name = landmark_data.get("name", "").lower()
                landmark_name_en = landmark_data.get("name_en", "").lower()
                
                for keyword in keywords:
                    keyword_lower = keyword.lower()
                    
                    # 检查关键词匹配
                    for lm_keyword in landmark_keywords:
                        if keyword_lower in lm_keyword.lower() or lm_keyword.lower() in keyword_lower:
                            score += 2
                    
                    # 检查名称匹配
                    if keyword_lower in landmark_name or keyword_lower in landmark_name_en:
                        score += 3
                    
                    # 检查描述匹配
                    description = landmark_data.get("description", "").lower()
                    if keyword_lower in description:
                        score += 1
                
                if score > 0:
                    landmark_result = landmark_data.copy()
                    landmark_result["landmark_id"] = landmark_id
                    landmark_result["match_score"] = score
                    landmark_result["confidence"] = min(score / 5, 1.0)
                    matching_landmarks.append(landmark_result)
            
            # 按匹配分数排序
            matching_landmarks.sort(key=lambda x: x["match_score"], reverse=True)
            
            return matching_landmarks[:10]  # 返回前10个匹配结果
            
        except Exception as e:
            self.logger.error(f"关键词搜索失败: {str(e)}")
            return []
    
    def search_landmarks_by_location(
        self,
        latitude: float,
        longitude: float,
        radius_km: float = 50
    ) -> List[Dict[str, Any]]:
        """根据地理位置搜索附近的地标"""
        nearby_landmarks = []
        
        try:
            for landmark_id, landmark_data in self.landmarks.items():
                location = landmark_data.get("location", {})
                lm_lat = location.get("latitude")
                lm_lng = location.get("longitude")
                
                if lm_lat is not None and lm_lng is not None:
                    distance = self._calculate_distance(latitude, longitude, lm_lat, lm_lng)
                    
                    if distance <= radius_km:
                        landmark_result = landmark_data.copy()
                        landmark_result["landmark_id"] = landmark_id
                        landmark_result["distance_km"] = distance
                        landmark_result["confidence"] = max(1 - (distance / radius_km), 0.1)
                        nearby_landmarks.append(landmark_result)
            
            # 按距离排序
            nearby_landmarks.sort(key=lambda x: x["distance_km"])
            
            return nearby_landmarks
            
        except Exception as e:
            self.logger.error(f"位置搜索失败: {str(e)}")
            return []
    
    def search_landmarks_by_visual_features(self, visual_features: List[str]) -> List[Dict[str, Any]]:
        """根据视觉特征搜索地标"""
        matching_landmarks = []
        
        try:
            for landmark_id, landmark_data in self.landmarks.items():
                score = 0
                landmark_features = landmark_data.get("visual_features", [])
                
                for feature in visual_features:
                    feature_lower = feature.lower()
                    
                    for lm_feature in landmark_features:
                        if feature_lower in lm_feature.lower() or lm_feature.lower() in feature_lower:
                            score += 1
                
                if score > 0:
                    landmark_result = landmark_data.copy()
                    landmark_result["landmark_id"] = landmark_id
                    landmark_result["feature_match_score"] = score
                    landmark_result["confidence"] = min(score / len(landmark_features), 1.0)
                    matching_landmarks.append(landmark_result)
            
            # 按特征匹配分数排序
            matching_landmarks.sort(key=lambda x: x["feature_match_score"], reverse=True)
            
            return matching_landmarks[:5]  # 返回前5个匹配结果
            
        except Exception as e:
            self.logger.error(f"视觉特征搜索失败: {str(e)}")
            return []
    
    def get_landmark_by_id(self, landmark_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取地标信息"""
        return self.landmarks.get(landmark_id)
    
    def get_landmarks_by_country(self, country: str) -> List[Dict[str, Any]]:
        """获取指定国家的地标"""
        country_landmarks = []
        
        try:
            country_lower = country.lower()
            
            for landmark_id, landmark_data in self.landmarks.items():
                location = landmark_data.get("location", {})
                landmark_country = location.get("country", "").lower()
                
                if country_lower in landmark_country or landmark_country in country_lower:
                    landmark_result = landmark_data.copy()
                    landmark_result["landmark_id"] = landmark_id
                    country_landmarks.append(landmark_result)
            
            return country_landmarks
            
        except Exception as e:
            self.logger.error(f"国家地标搜索失败: {str(e)}")
            return []
    
    def comprehensive_search(
        self,
        keywords: List[str] = None,
        visual_features: List[str] = None,
        latitude: float = None,
        longitude: float = None,
        radius_km: float = 50
    ) -> List[Dict[str, Any]]:
        """综合搜索地标"""
        all_results = []
        
        try:
            # 关键词搜索
            if keywords:
                keyword_results = self.search_landmarks_by_keywords(keywords)
                for result in keyword_results:
                    result["search_type"] = "keyword"
                all_results.extend(keyword_results)
            
            # 视觉特征搜索
            if visual_features:
                feature_results = self.search_landmarks_by_visual_features(visual_features)
                for result in feature_results:
                    result["search_type"] = "visual"
                all_results.extend(feature_results)
            
            # 位置搜索
            if latitude is not None and longitude is not None:
                location_results = self.search_landmarks_by_location(latitude, longitude, radius_km)
                for result in location_results:
                    result["search_type"] = "location"
                all_results.extend(location_results)
            
            # 合并重复结果并计算综合分数
            merged_results = self._merge_search_results(all_results)
            
            # 按综合分数排序
            merged_results.sort(key=lambda x: x.get("combined_score", 0), reverse=True)
            
            return merged_results[:10]  # 返回前10个结果
            
        except Exception as e:
            self.logger.error(f"综合搜索失败: {str(e)}")
            return []
    
    def _merge_search_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """合并搜索结果"""
        merged = {}
        
        for result in results:
            landmark_id = result.get("landmark_id")
            if landmark_id not in merged:
                merged[landmark_id] = result.copy()
                merged[landmark_id]["search_types"] = [result.get("search_type", "unknown")]
                merged[landmark_id]["combined_score"] = result.get("confidence", 0)
            else:
                # 合并搜索类型
                search_type = result.get("search_type", "unknown")
                if search_type not in merged[landmark_id]["search_types"]:
                    merged[landmark_id]["search_types"].append(search_type)
                
                # 更新综合分数
                current_score = merged[landmark_id]["combined_score"]
                new_score = result.get("confidence", 0)
                merged[landmark_id]["combined_score"] = (current_score + new_score) / 2
        
        return list(merged.values())
    
    def _calculate_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """计算两点间的距离（公里）"""
        # 使用Haversine公式计算球面距离
        R = 6371  # 地球半径（公里）
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lng = math.radians(lng2 - lng1)
        
        a = (math.sin(delta_lat / 2) ** 2 +
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(delta_lng / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c
    
    def add_landmark(
        self,
        landmark_id: str,
        name: str,
        name_en: str,
        country: str,
        city: str,
        latitude: float,
        longitude: float,
        category: str,
        description: str,
        keywords: List[str],
        visual_features: List[str],
        **kwargs
    ) -> None:
        """添加新地标"""
        try:
            landmark_data = {
                "name": name,
                "name_en": name_en,
                "location": {
                    "country": country,
                    "city": city,
                    "latitude": latitude,
                    "longitude": longitude
                },
                "category": category,
                "description": description,
                "keywords": keywords,
                "visual_features": visual_features
            }
            
            # 添加其他属性
            landmark_data.update(kwargs)
            
            self.landmarks[landmark_id] = landmark_data
            self._save_database()
            
            self.logger.info(f"已添加地标: {landmark_id} - {name}")
            
        except Exception as e:
            self.logger.error(f"添加地标失败: {str(e)}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            stats = {
                "total_landmarks": len(self.landmarks),
                "categories": {},
                "countries": {},
                "cities": {}
            }
            
            for landmark_data in self.landmarks.values():
                # 统计类别
                category = landmark_data.get("category", "unknown")
                stats["categories"][category] = stats["categories"].get(category, 0) + 1
                
                # 统计国家
                location = landmark_data.get("location", {})
                country = location.get("country", "unknown")
                stats["countries"][country] = stats["countries"].get(country, 0) + 1
                
                # 统计城市
                city = location.get("city", "unknown")
                stats["cities"][city] = stats["cities"].get(city, 0) + 1
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {str(e)}")
            return {}


# 全局地标数据库实例
landmark_db = LandmarkDatabase()
