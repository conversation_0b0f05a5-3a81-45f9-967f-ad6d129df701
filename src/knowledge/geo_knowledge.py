"""
地理知识库模块
提供地理位置相关的知识和推理支持
"""

import json
from typing import Dict, Any, List, Optional
from pathlib import Path

from ..utils import app_logger


class GeoKnowledgeBase:
    """地理知识库"""
    
    def __init__(self, knowledge_path: str = "data/geo_knowledge.json"):
        self.knowledge_path = knowledge_path
        self.logger = app_logger
        self.knowledge_data = {}
        self._load_knowledge()
    
    def _load_knowledge(self) -> None:
        """加载地理知识数据"""
        try:
            knowledge_file = Path(self.knowledge_path)
            if knowledge_file.exists():
                with open(knowledge_file, 'r', encoding='utf-8') as f:
                    self.knowledge_data = json.load(f)
                self.logger.info(f"地理知识库加载完成: {len(self.knowledge_data)} 条记录")
            else:
                self.logger.warning(f"地理知识库文件不存在: {self.knowledge_path}")
                self._create_default_knowledge()
        except Exception as e:
            self.logger.error(f"加载地理知识库失败: {str(e)}")
            self._create_default_knowledge()
    
    def _create_default_knowledge(self) -> None:
        """创建默认地理知识"""
        self.knowledge_data = {
            "climate_regions": {
                "tropical": {
                    "characteristics": ["高温", "高湿", "热带植物", "棕榈树"],
                    "typical_locations": ["东南亚", "南美洲", "非洲中部", "太平洋岛屿"],
                    "vegetation": ["热带雨林", "棕榈树", "香蕉树", "椰子树"]
                },
                "temperate": {
                    "characteristics": ["四季分明", "温带植物", "落叶树"],
                    "typical_locations": ["中国大部分地区", "欧洲", "北美洲", "日本"],
                    "vegetation": ["落叶林", "针叶林", "草原"]
                },
                "arid": {
                    "characteristics": ["干燥", "少雨", "沙漠植物", "强烈阳光"],
                    "typical_locations": ["中东", "北非", "澳洲内陆", "美国西南部"],
                    "vegetation": ["仙人掌", "沙漠灌木", "稀疏草地"]
                }
            },
            "architectural_styles": {
                "chinese_traditional": {
                    "features": ["飞檐翘角", "红墙黄瓦", "斗拱结构", "庭院布局"],
                    "typical_locations": ["中国", "台湾", "香港", "澳门"],
                    "materials": ["木材", "青砖", "琉璃瓦"]
                },
                "european_classical": {
                    "features": ["石材建筑", "哥特式", "巴洛克", "圆顶"],
                    "typical_locations": ["欧洲", "俄罗斯", "前殖民地"],
                    "materials": ["石材", "大理石", "铁艺"]
                },
                "modern_glass": {
                    "features": ["玻璃幕墙", "钢结构", "简约线条", "高层建筑"],
                    "typical_locations": ["全球大城市", "商业区", "金融中心"],
                    "materials": ["玻璃", "钢材", "混凝土"]
                }
            },
            "cultural_indicators": {
                "text_languages": {
                    "chinese": ["中文", "汉字", "繁体字", "简体字"],
                    "english": ["英文", "拉丁字母"],
                    "arabic": ["阿拉伯文", "从右到左"],
                    "japanese": ["日文", "假名", "汉字"],
                    "korean": ["韩文", "한글"]
                },
                "traffic_patterns": {
                    "left_driving": ["英国", "日本", "澳大利亚", "印度", "香港"],
                    "right_driving": ["中国大陆", "美国", "欧洲大陆", "俄罗斯"]
                }
            },
            "landmark_categories": {
                "religious": ["教堂", "寺庙", "清真寺", "佛塔", "神社"],
                "historical": ["古城墙", "城堡", "宫殿", "古迹", "纪念碑"],
                "modern": ["摩天大楼", "桥梁", "体育场", "机场", "火车站"],
                "natural": ["山峰", "湖泊", "海岸", "瀑布", "峡谷"]
            }
        }
        
        # 保存默认知识到文件
        self._save_knowledge()
    
    def _save_knowledge(self) -> None:
        """保存知识库到文件"""
        try:
            knowledge_file = Path(self.knowledge_path)
            knowledge_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(knowledge_file, 'w', encoding='utf-8') as f:
                json.dump(self.knowledge_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"地理知识库已保存: {self.knowledge_path}")
        except Exception as e:
            self.logger.error(f"保存地理知识库失败: {str(e)}")
    
    def infer_climate_from_features(self, features: Dict[str, Any]) -> List[str]:
        """根据特征推断气候类型"""
        possible_climates = []
        
        try:
            vegetation = features.get("vegetation", "").lower()
            lighting = features.get("lighting", "").lower()
            
            climate_regions = self.knowledge_data.get("climate_regions", {})
            
            for climate, data in climate_regions.items():
                score = 0
                characteristics = [c.lower() for c in data.get("characteristics", [])]
                vegetation_types = [v.lower() for v in data.get("vegetation", [])]
                
                # 检查植被匹配
                for veg_type in vegetation_types:
                    if veg_type in vegetation:
                        score += 2
                
                # 检查特征匹配
                for char in characteristics:
                    if char in str(features).lower():
                        score += 1
                
                if score > 0:
                    possible_climates.append({
                        "climate": climate,
                        "score": score,
                        "confidence": min(score / 5, 1.0)
                    })
            
            # 按分数排序
            possible_climates.sort(key=lambda x: x["score"], reverse=True)
            return [c["climate"] for c in possible_climates[:3]]
            
        except Exception as e:
            self.logger.error(f"气候推断失败: {str(e)}")
            return []
    
    def infer_region_from_architecture(self, architectural_features: List[str]) -> List[str]:
        """根据建筑特征推断地区"""
        possible_regions = []
        
        try:
            arch_styles = self.knowledge_data.get("architectural_styles", {})
            
            for style, data in arch_styles.items():
                score = 0
                features = [f.lower() for f in data.get("features", [])]
                
                for arch_feature in architectural_features:
                    arch_feature_lower = arch_feature.lower()
                    for feature in features:
                        if feature in arch_feature_lower or arch_feature_lower in feature:
                            score += 1
                
                if score > 0:
                    locations = data.get("typical_locations", [])
                    possible_regions.extend(locations)
            
            # 去重并返回
            return list(set(possible_regions))
            
        except Exception as e:
            self.logger.error(f"建筑地区推断失败: {str(e)}")
            return []
    
    def infer_region_from_language(self, detected_languages: List[str]) -> List[str]:
        """根据语言推断地区"""
        possible_regions = []
        
        try:
            text_languages = self.knowledge_data.get("cultural_indicators", {}).get("text_languages", {})
            
            for detected_lang in detected_languages:
                detected_lang_lower = detected_lang.lower()
                
                for region, lang_indicators in text_languages.items():
                    for indicator in lang_indicators:
                        if indicator.lower() in detected_lang_lower:
                            # 根据语言映射到地区
                            region_mapping = {
                                "chinese": ["中国", "台湾", "香港", "澳门", "新加坡"],
                                "english": ["美国", "英国", "加拿大", "澳大利亚", "新西兰"],
                                "japanese": ["日本"],
                                "korean": ["韩国"],
                                "arabic": ["中东地区", "北非"]
                            }
                            
                            if region in region_mapping:
                                possible_regions.extend(region_mapping[region])
            
            return list(set(possible_regions))
            
        except Exception as e:
            self.logger.error(f"语言地区推断失败: {str(e)}")
            return []
    
    def get_landmark_category(self, landmark_name: str) -> Optional[str]:
        """获取地标类别"""
        try:
            landmark_categories = self.knowledge_data.get("landmark_categories", {})
            landmark_lower = landmark_name.lower()
            
            for category, keywords in landmark_categories.items():
                for keyword in keywords:
                    if keyword.lower() in landmark_lower:
                        return category
            
            return None
            
        except Exception as e:
            self.logger.error(f"地标分类失败: {str(e)}")
            return None
    
    def validate_location_consistency(
        self,
        proposed_location: str,
        climate: str,
        architecture: List[str],
        languages: List[str]
    ) -> Dict[str, Any]:
        """验证位置与特征的一致性"""
        
        consistency_report = {
            "is_consistent": True,
            "consistency_score": 1.0,
            "issues": [],
            "supporting_evidence": []
        }
        
        try:
            proposed_lower = proposed_location.lower()
            
            # 检查气候一致性
            if climate:
                climate_regions = self.knowledge_data.get("climate_regions", {}).get(climate, {})
                typical_locations = [loc.lower() for loc in climate_regions.get("typical_locations", [])]
                
                climate_consistent = any(loc in proposed_lower for loc in typical_locations)
                if not climate_consistent:
                    consistency_report["issues"].append(f"气候类型 '{climate}' 与位置 '{proposed_location}' 不一致")
                    consistency_report["consistency_score"] *= 0.8
                else:
                    consistency_report["supporting_evidence"].append(f"气候类型 '{climate}' 与位置一致")
            
            # 检查建筑风格一致性
            if architecture:
                arch_consistent = False
                for arch_feature in architecture:
                    inferred_regions = self.infer_region_from_architecture([arch_feature])
                    if any(region.lower() in proposed_lower for region in inferred_regions):
                        arch_consistent = True
                        consistency_report["supporting_evidence"].append(f"建筑特征 '{arch_feature}' 支持该位置")
                        break
                
                if not arch_consistent and architecture:
                    consistency_report["issues"].append("建筑风格与推测位置不一致")
                    consistency_report["consistency_score"] *= 0.7
            
            # 检查语言一致性
            if languages:
                lang_consistent = False
                for language in languages:
                    inferred_regions = self.infer_region_from_language([language])
                    if any(region.lower() in proposed_lower for region in inferred_regions):
                        lang_consistent = True
                        consistency_report["supporting_evidence"].append(f"语言 '{language}' 支持该位置")
                        break
                
                if not lang_consistent and languages:
                    consistency_report["issues"].append("语言特征与推测位置不一致")
                    consistency_report["consistency_score"] *= 0.6
            
            # 更新一致性状态
            consistency_report["is_consistent"] = len(consistency_report["issues"]) == 0
            
            return consistency_report
            
        except Exception as e:
            self.logger.error(f"一致性验证失败: {str(e)}")
            return {
                "is_consistent": False,
                "consistency_score": 0.0,
                "issues": [f"验证过程出错: {str(e)}"],
                "supporting_evidence": []
            }
    
    def get_regional_knowledge(self, region: str) -> Dict[str, Any]:
        """获取特定地区的知识"""
        try:
            # 这里可以扩展为更详细的地区知识库
            regional_info = {
                "climate_info": {},
                "cultural_info": {},
                "architectural_info": {},
                "geographical_info": {}
            }
            
            region_lower = region.lower()
            
            # 查找气候信息
            climate_regions = self.knowledge_data.get("climate_regions", {})
            for climate, data in climate_regions.items():
                typical_locations = [loc.lower() for loc in data.get("typical_locations", [])]
                if any(loc in region_lower for loc in typical_locations):
                    regional_info["climate_info"] = {
                        "climate_type": climate,
                        "characteristics": data.get("characteristics", []),
                        "vegetation": data.get("vegetation", [])
                    }
                    break
            
            return regional_info
            
        except Exception as e:
            self.logger.error(f"获取地区知识失败: {str(e)}")
            return {}
    
    def add_knowledge(self, category: str, key: str, data: Dict[str, Any]) -> None:
        """添加新的知识条目"""
        try:
            if category not in self.knowledge_data:
                self.knowledge_data[category] = {}
            
            self.knowledge_data[category][key] = data
            self._save_knowledge()
            
            self.logger.info(f"已添加知识条目: {category}.{key}")
            
        except Exception as e:
            self.logger.error(f"添加知识失败: {str(e)}")


# 全局知识库实例
geo_knowledge = GeoKnowledgeBase()
