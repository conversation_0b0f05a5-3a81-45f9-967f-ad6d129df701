"""
搜索Actor
负责基于图片线索进行信息搜索和验证
"""

import json
from typing import Dict, Any, List
from datetime import datetime

from .base_actor import BaseActor
from ..core.context_manager import TaskResult, TaskStatus
from ..prompts import PromptTemplates
from ..tools.search_tools import SearchToolsManager
from ..utils import config


class SearchActor(BaseActor):
    """搜索Actor"""
    
    def __init__(self, llm_client=None):
        super().__init__("search", llm_client)
        self.search_manager = SearchToolsManager()
    
    async def execute(self, task_input: Dict[str, Any]) -> TaskResult:
        """执行搜索任务"""
        
        # 验证输入
        required_keys = ["clues"]
        if not self._validate_input(task_input, required_keys):
            return TaskResult(
                task_id="",
                actor_type=self.actor_type,
                status=TaskStatus.FAILED,
                result={},
                confidence=0.0,
                timestamp=datetime.now(),
                execution_time=0.0,
                error_message="缺少必需的输入参数"
            )
        
        clues = task_input["clues"]
        search_strategies = task_input.get("search_strategies", ["text_search", "reverse_image"])
        image_path = task_input.get("image_path")
        
        try:
            # 1. 分析线索并生成搜索查询
            search_queries = await self._generate_search_queries(clues)
            
            # 2. 执行多策略搜索
            search_results = await self._execute_multi_strategy_search(
                search_queries, search_strategies, image_path
            )
            
            # 3. 验证和过滤搜索结果
            verified_results = await self._verify_search_results(search_results, clues)
            
            # 4. 分析搜索结果并提取地理位置信息
            location_analysis = await self._analyze_location_results(verified_results)
            
            # 5. 整合搜索结果
            final_result = self._integrate_search_results(
                search_queries, verified_results, location_analysis
            )
            
            # 计算置信度
            confidence = self._calculate_search_confidence(final_result)
            
            return TaskResult(
                task_id="",
                actor_type=self.actor_type,
                status=TaskStatus.COMPLETED,
                result=final_result,
                confidence=confidence,
                timestamp=datetime.now(),
                execution_time=0.0
            )
            
        except Exception as e:
            self.logger.error(f"搜索任务失败: {str(e)}")
            return TaskResult(
                task_id="",
                actor_type=self.actor_type,
                status=TaskStatus.FAILED,
                result={},
                confidence=0.0,
                timestamp=datetime.now(),
                execution_time=0.0,
                error_message=str(e)
            )
    
    async def _generate_search_queries(self, clues: Dict[str, Any]) -> Dict[str, List[str]]:
        """基于线索生成搜索查询"""
        try:
            queries = {
                "text_queries": [],
                "location_queries": [],
                "landmark_queries": []
            }
            
            # 从文字线索生成查询
            text_clues = clues.get("text_clues", [])
            geographic_texts = clues.get("geographic_texts", [])
            
            for text in text_clues + geographic_texts:
                if text and len(text.strip()) > 2:
                    queries["text_queries"].append(text.strip())
                    
                    # 如果是地理相关文字，也添加到位置查询
                    if text in geographic_texts:
                        queries["location_queries"].append(text.strip())
            
            # 从地标线索生成查询
            landmarks = clues.get("landmarks", [])
            for landmark in landmarks:
                if isinstance(landmark, dict):
                    landmark_name = landmark.get("name", "")
                    if landmark_name:
                        queries["landmark_queries"].append(landmark_name)
                elif isinstance(landmark, str):
                    queries["landmark_queries"].append(landmark)
            
            # 从建筑特征生成查询
            architectural_features = clues.get("architectural_features", [])
            for feature in architectural_features:
                if isinstance(feature, str) and len(feature) > 5:
                    queries["text_queries"].append(f"architecture {feature}")
            
            # 从自然环境特征生成查询
            natural_env = clues.get("natural_environment", {})
            if isinstance(natural_env, dict):
                vegetation = natural_env.get("vegetation")
                terrain = natural_env.get("terrain")
                climate = natural_env.get("climate")
                
                if vegetation and vegetation != "unknown":
                    queries["text_queries"].append(f"{vegetation} vegetation landscape")
                
                if terrain and terrain != "unknown":
                    queries["text_queries"].append(f"{terrain} terrain geography")
                
                if climate and climate != "unknown":
                    queries["text_queries"].append(f"{climate} climate region")
            
            self.logger.info(f"生成搜索查询: {sum(len(v) for v in queries.values())} 个")
            return queries
            
        except Exception as e:
            self.logger.error(f"生成搜索查询失败: {str(e)}")
            return {"text_queries": [], "location_queries": [], "landmark_queries": []}
    
    async def _execute_multi_strategy_search(
        self,
        search_queries: Dict[str, List[str]],
        strategies: List[str],
        image_path: str = None
    ) -> Dict[str, List[Dict[str, Any]]]:
        """执行多策略搜索"""
        
        search_results = {
            "text_results": [],
            "image_results": [],
            "reverse_image_results": [],
            "location_results": []
        }
        
        try:
            # 文本搜索
            if "text_search" in strategies:
                for query in search_queries.get("text_queries", []):
                    results = await self.search_manager.search_text(query)
                    search_results["text_results"].extend(results)
            
            # 图片搜索
            if "image_search" in strategies:
                for query in search_queries.get("text_queries", []):
                    results = await self.search_manager.search_images(query)
                    search_results["image_results"].extend(results)
            
            # 反向图片搜索
            if "reverse_image" in strategies and image_path:
                results = await self.search_manager.reverse_image_search(image_path)
                search_results["reverse_image_results"].extend(results)
            
            # 地理位置搜索
            if "location_search" in strategies:
                location_queries = (search_queries.get("location_queries", []) + 
                                  search_queries.get("landmark_queries", []))
                for query in location_queries:
                    results = await self.search_manager.search_location(query)
                    search_results["location_results"].extend(results)
            
            self.logger.info(f"搜索完成，总结果数: {sum(len(v) for v in search_results.values())}")
            return search_results
            
        except Exception as e:
            self.logger.error(f"多策略搜索失败: {str(e)}")
            return search_results
    
    async def _verify_search_results(
        self,
        search_results: Dict[str, List[Dict[str, Any]]],
        original_clues: Dict[str, Any]
    ) -> Dict[str, List[Dict[str, Any]]]:
        """验证和过滤搜索结果"""
        
        verified_results = {
            "verified_text_results": [],
            "verified_image_results": [],
            "verified_reverse_results": [],
            "verified_location_results": []
        }
        
        try:
            # 验证文本搜索结果
            for result in search_results.get("text_results", []):
                if self._is_relevant_result(result, original_clues):
                    verified_results["verified_text_results"].append(result)
            
            # 验证图片搜索结果
            for result in search_results.get("image_results", []):
                if self._is_relevant_image_result(result, original_clues):
                    verified_results["verified_image_results"].append(result)
            
            # 验证反向图片搜索结果
            for result in search_results.get("reverse_image_results", []):
                verified_results["verified_reverse_results"].append(result)
            
            # 验证地理位置搜索结果
            for result in search_results.get("location_results", []):
                if self._is_relevant_location_result(result, original_clues):
                    verified_results["verified_location_results"].append(result)
            
            self.logger.info(f"结果验证完成，保留: {sum(len(v) for v in verified_results.values())} 个")
            return verified_results
            
        except Exception as e:
            self.logger.error(f"搜索结果验证失败: {str(e)}")
            return verified_results
    
    async def _analyze_location_results(
        self,
        verified_results: Dict[str, List[Dict[str, Any]]]
    ) -> Dict[str, Any]:
        """分析搜索结果中的地理位置信息"""
        
        location_candidates = []
        confidence_scores = {}
        
        try:
            # 从地理位置搜索结果中提取候选位置
            for result in verified_results.get("verified_location_results", []):
                if "latitude" in result and "longitude" in result:
                    location = {
                        "name": result.get("name", ""),
                        "latitude": result["latitude"],
                        "longitude": result["longitude"],
                        "type": result.get("type", ""),
                        "source": "location_search",
                        "confidence": result.get("importance", 0.5)
                    }
                    location_candidates.append(location)
            
            # 从文本搜索结果中提取地理信息
            for result in verified_results.get("verified_text_results", []):
                location_info = self._extract_location_from_text(result)
                if location_info:
                    location_candidates.append(location_info)
            
            # 从反向图片搜索结果中提取地理信息
            for result in verified_results.get("verified_reverse_results", []):
                location_info = self._extract_location_from_reverse_search(result)
                if location_info:
                    location_candidates.append(location_info)
            
            # 聚类和排序候选位置
            clustered_locations = self._cluster_locations(location_candidates)
            
            return {
                "location_candidates": location_candidates,
                "clustered_locations": clustered_locations,
                "total_candidates": len(location_candidates)
            }
            
        except Exception as e:
            self.logger.error(f"位置分析失败: {str(e)}")
            return {"location_candidates": [], "clustered_locations": []}
    
    def _integrate_search_results(
        self,
        search_queries: Dict[str, List[str]],
        verified_results: Dict[str, List[Dict[str, Any]]],
        location_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """整合搜索结果"""
        
        return {
            "search_queries": search_queries,
            "search_results": verified_results,
            "location_analysis": location_analysis,
            "verification_notes": self._generate_verification_notes(verified_results),
            "recommended_locations": self._recommend_locations(location_analysis),
            "search_summary": {
                "total_queries": sum(len(v) for v in search_queries.values()),
                "total_results": sum(len(v) for v in verified_results.values()),
                "location_candidates": len(location_analysis.get("location_candidates", []))
            }
        }
    
    def _calculate_search_confidence(self, search_result: Dict[str, Any]) -> float:
        """计算搜索置信度"""
        confidence_factors = []
        
        # 基于搜索结果数量
        total_results = search_result.get("search_summary", {}).get("total_results", 0)
        if total_results > 0:
            result_confidence = min(total_results / 10, 1.0)
            confidence_factors.append(result_confidence)
        
        # 基于位置候选数量
        location_candidates = search_result.get("location_analysis", {}).get("total_candidates", 0)
        if location_candidates > 0:
            location_confidence = min(location_candidates / 5, 1.0)
            confidence_factors.append(location_confidence)
        
        # 基于推荐位置的质量
        recommended_locations = search_result.get("recommended_locations", [])
        if recommended_locations:
            avg_location_confidence = sum(
                loc.get("confidence", 0) for loc in recommended_locations
            ) / len(recommended_locations)
            confidence_factors.append(avg_location_confidence)
        
        # 计算最终置信度
        if confidence_factors:
            return sum(confidence_factors) / len(confidence_factors)
        else:
            return 0.2  # 基础置信度
    
    def _is_relevant_result(self, result: Dict[str, Any], clues: Dict[str, Any]) -> bool:
        """判断搜索结果是否相关"""
        # 简单的相关性检查
        title = result.get("title", "").lower()
        snippet = result.get("snippet", "").lower()
        
        # 检查是否包含地理相关关键词
        geo_keywords = ["location", "place", "city", "country", "street", "address", "map"]
        
        return any(keyword in title or keyword in snippet for keyword in geo_keywords)
    
    def _is_relevant_image_result(self, result: Dict[str, Any], clues: Dict[str, Any]) -> bool:
        """判断图片搜索结果是否相关"""
        # 基于图片标题和描述判断相关性
        return self._is_relevant_result(result, clues)
    
    def _is_relevant_location_result(self, result: Dict[str, Any], clues: Dict[str, Any]) -> bool:
        """判断地理位置搜索结果是否相关"""
        # 地理位置搜索结果通常都是相关的
        return True
    
    def _extract_location_from_text(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """从文本搜索结果中提取地理位置信息"""
        # 这里可以实现更复杂的地理信息提取逻辑
        # 暂时返回基础信息
        return {
            "name": result.get("title", ""),
            "source": "text_search",
            "confidence": 0.3,
            "url": result.get("url", "")
        }
    
    def _extract_location_from_reverse_search(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """从反向图片搜索结果中提取地理位置信息"""
        return {
            "name": result.get("title", ""),
            "source": "reverse_image_search",
            "confidence": result.get("similarity", 0.5),
            "url": result.get("url", "")
        }
    
    def _cluster_locations(self, locations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """聚类相似的地理位置"""
        # 简单的聚类实现
        clustered = []
        
        for location in locations:
            if location.get("latitude") and location.get("longitude"):
                # 检查是否与现有聚类接近
                added_to_cluster = False
                for cluster in clustered:
                    if self._are_locations_close(location, cluster):
                        cluster["members"].append(location)
                        cluster["confidence"] = max(cluster["confidence"], location.get("confidence", 0))
                        added_to_cluster = True
                        break
                
                if not added_to_cluster:
                    clustered.append({
                        "center": location,
                        "members": [location],
                        "confidence": location.get("confidence", 0)
                    })
        
        # 按置信度排序
        clustered.sort(key=lambda x: x["confidence"], reverse=True)
        return clustered
    
    def _are_locations_close(self, loc1: Dict[str, Any], cluster: Dict[str, Any]) -> bool:
        """判断两个位置是否接近"""
        center = cluster["center"]
        
        if not (loc1.get("latitude") and loc1.get("longitude") and 
                center.get("latitude") and center.get("longitude")):
            return False
        
        # 简单的距离计算（实际应用中可以使用更精确的地理距离计算）
        lat_diff = abs(loc1["latitude"] - center["latitude"])
        lng_diff = abs(loc1["longitude"] - center["longitude"])
        
        # 如果差异小于0.1度（约11公里），认为是接近的
        return lat_diff < 0.1 and lng_diff < 0.1
    
    def _generate_verification_notes(self, verified_results: Dict[str, List]) -> str:
        """生成验证说明"""
        notes = []
        
        for result_type, results in verified_results.items():
            if results:
                notes.append(f"{result_type}: {len(results)} 个结果")
        
        return "; ".join(notes)
    
    def _recommend_locations(self, location_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """推荐最可能的地理位置"""
        clustered_locations = location_analysis.get("clustered_locations", [])
        
        # 返回置信度最高的前3个位置
        return clustered_locations[:3]
