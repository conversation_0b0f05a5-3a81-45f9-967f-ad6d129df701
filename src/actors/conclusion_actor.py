"""
结论Actor
负责整合所有分析结果并给出最终的地理位置判断
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import statistics

from .base_actor import BaseActor
from ..core.context_manager import TaskResult, TaskStatus
from ..prompts import PromptTemplates
from ..utils import config


class ConclusionActor(BaseActor):
    """结论Actor"""
    
    def __init__(self, llm_client=None):
        super().__init__("conclusion", llm_client)
    
    async def execute(self, task_input: Dict[str, Any]) -> TaskResult:
        """执行结论整合任务"""
        
        # 验证输入
        required_keys = ["image_analysis", "search_results", "reflection_results"]
        if not self._validate_input(task_input, required_keys):
            return TaskResult(
                task_id="",
                actor_type=self.actor_type,
                status=TaskStatus.FAILED,
                result={},
                confidence=0.0,
                timestamp=datetime.now(),
                execution_time=0.0,
                error_message="缺少必需的输入参数"
            )
        
        image_analysis = task_input["image_analysis"]
        search_results = task_input["search_results"]
        reflection_results = task_input["reflection_results"]
        require_evidence = task_input.get("require_evidence", True)
        
        try:
            # 1. 整合所有证据
            integrated_evidence = await self._integrate_all_evidence(
                image_analysis, search_results, reflection_results
            )
            
            # 2. 确定最可能的地理位置
            primary_location = await self._determine_primary_location(integrated_evidence)
            
            # 3. 构建推理过程
            reasoning_process = await self._build_reasoning_process(
                integrated_evidence, primary_location
            )
            
            # 4. 评估结论置信度
            conclusion_confidence = await self._evaluate_conclusion_confidence(
                primary_location, integrated_evidence, reflection_results
            )
            
            # 5. 识别不确定因素
            uncertainty_factors = await self._identify_uncertainty_factors(
                integrated_evidence, reflection_results
            )
            
            # 6. 生成替代可能性
            alternative_possibilities = await self._generate_alternative_possibilities(
                integrated_evidence, primary_location
            )
            
            # 7. 构建最终结论
            final_conclusion = await self._build_final_conclusion(
                primary_location,
                reasoning_process,
                conclusion_confidence,
                integrated_evidence,
                uncertainty_factors,
                alternative_possibilities
            )
            
            # 验证结论质量
            if require_evidence and not self._validate_conclusion_quality(final_conclusion):
                return TaskResult(
                    task_id="",
                    actor_type=self.actor_type,
                    status=TaskStatus.FAILED,
                    result=final_conclusion,
                    confidence=0.0,
                    timestamp=datetime.now(),
                    execution_time=0.0,
                    error_message="结论质量不满足要求"
                )
            
            return TaskResult(
                task_id="",
                actor_type=self.actor_type,
                status=TaskStatus.COMPLETED,
                result=final_conclusion,
                confidence=conclusion_confidence,
                timestamp=datetime.now(),
                execution_time=0.0
            )
            
        except Exception as e:
            self.logger.error(f"结论整合任务失败: {str(e)}")
            return TaskResult(
                task_id="",
                actor_type=self.actor_type,
                status=TaskStatus.FAILED,
                result={},
                confidence=0.0,
                timestamp=datetime.now(),
                execution_time=0.0,
                error_message=str(e)
            )
    
    async def _integrate_all_evidence(
        self,
        image_analysis: Dict[str, Any],
        search_results: Dict[str, Any],
        reflection_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """整合所有证据"""
        
        try:
            # 从图片分析中提取证据
            visual_evidence = {
                "text_clues": image_analysis.get("text_clues", []),
                "geographic_texts": image_analysis.get("geographic_texts", []),
                "landmarks": image_analysis.get("landmarks", []),
                "architectural_features": image_analysis.get("architectural_features", []),
                "natural_environment": image_analysis.get("natural_environment", {}),
                "cultural_features": image_analysis.get("cultural_features", [])
            }
            
            # 从搜索结果中提取证据
            search_evidence = {
                "location_candidates": search_results.get("location_analysis", {}).get("location_candidates", []),
                "clustered_locations": search_results.get("location_analysis", {}).get("clustered_locations", []),
                "verified_results": search_results.get("search_results", {}),
                "recommended_locations": search_results.get("recommended_locations", [])
            }
            
            # 从反思结果中提取证据
            reflection_evidence = {
                "consistency_assessment": reflection_results.get("consistency_check", {}),
                "evidence_sufficiency": reflection_results.get("evidence_sufficiency", {}),
                "alternative_hypotheses": reflection_results.get("alternative_hypotheses", []),
                "confidence_adjustment": reflection_results.get("confidence_assessment", 0.0)
            }
            
            # 计算证据权重
            evidence_weights = self._calculate_evidence_weights(
                visual_evidence, search_evidence, reflection_evidence
            )
            
            return {
                "visual_evidence": visual_evidence,
                "search_evidence": search_evidence,
                "reflection_evidence": reflection_evidence,
                "evidence_weights": evidence_weights,
                "total_evidence_score": sum(evidence_weights.values())
            }
            
        except Exception as e:
            self.logger.error(f"证据整合失败: {str(e)}")
            return {"visual_evidence": {}, "search_evidence": {}, "reflection_evidence": {}}
    
    async def _determine_primary_location(self, integrated_evidence: Dict[str, Any]) -> Dict[str, Any]:
        """确定主要地理位置"""
        
        try:
            location_candidates = []
            
            # 从搜索证据中获取位置候选
            search_evidence = integrated_evidence.get("search_evidence", {})
            clustered_locations = search_evidence.get("clustered_locations", [])
            
            for cluster in clustered_locations:
                center = cluster.get("center", {})
                if center.get("latitude") and center.get("longitude"):
                    location_candidates.append({
                        "source": "search_cluster",
                        "name": center.get("name", ""),
                        "latitude": center["latitude"],
                        "longitude": center["longitude"],
                        "confidence": cluster.get("confidence", 0.0),
                        "supporting_evidence": cluster.get("members", [])
                    })
            
            # 从推荐位置中获取候选
            recommended_locations = search_evidence.get("recommended_locations", [])
            for location in recommended_locations:
                if location.get("center", {}).get("latitude"):
                    center = location["center"]
                    location_candidates.append({
                        "source": "recommendation",
                        "name": center.get("name", ""),
                        "latitude": center["latitude"],
                        "longitude": center["longitude"],
                        "confidence": location.get("confidence", 0.0),
                        "supporting_evidence": location.get("members", [])
                    })
            
            # 如果没有明确的坐标位置，尝试从文字线索推断
            if not location_candidates:
                visual_evidence = integrated_evidence.get("visual_evidence", {})
                geographic_texts = visual_evidence.get("geographic_texts", [])
                
                if geographic_texts:
                    # 基于地理文字创建候选位置
                    for text in geographic_texts[:3]:  # 最多取前3个
                        location_candidates.append({
                            "source": "text_inference",
                            "name": text,
                            "latitude": None,
                            "longitude": None,
                            "confidence": 0.4,
                            "supporting_evidence": [f"文字线索: {text}"]
                        })
            
            # 选择最佳候选位置
            if location_candidates:
                # 按置信度排序
                location_candidates.sort(key=lambda x: x.get("confidence", 0), reverse=True)
                primary_location = location_candidates[0]
                
                # 增强主要位置的信息
                primary_location["alternative_candidates"] = location_candidates[1:4]  # 保留前3个替代选项
                primary_location["selection_reason"] = self._explain_location_selection(primary_location, location_candidates)
                
                return primary_location
            else:
                # 如果没有找到候选位置，返回未知位置
                return {
                    "source": "unknown",
                    "name": "位置未确定",
                    "latitude": None,
                    "longitude": None,
                    "confidence": 0.0,
                    "supporting_evidence": [],
                    "selection_reason": "未找到足够的地理位置证据"
                }
                
        except Exception as e:
            self.logger.error(f"确定主要位置失败: {str(e)}")
            return {
                "source": "error",
                "name": "位置确定失败",
                "latitude": None,
                "longitude": None,
                "confidence": 0.0,
                "supporting_evidence": [],
                "selection_reason": f"位置确定过程出错: {str(e)}"
            }
    
    async def _build_reasoning_process(
        self,
        integrated_evidence: Dict[str, Any],
        primary_location: Dict[str, Any]
    ) -> str:
        """构建推理过程"""
        
        try:
            reasoning_steps = []
            
            # 步骤1: 图片分析阶段
            visual_evidence = integrated_evidence.get("visual_evidence", {})
            
            text_clues = visual_evidence.get("text_clues", [])
            if text_clues:
                reasoning_steps.append(f"1. 图片文字分析: 识别出{len(text_clues)}个文字线索，包括: {', '.join(text_clues[:3])}")
            
            landmarks = visual_evidence.get("landmarks", [])
            if landmarks:
                landmark_names = [lm.get("name", str(lm)) for lm in landmarks if lm]
                reasoning_steps.append(f"2. 地标识别: 发现{len(landmarks)}个可能的地标，包括: {', '.join(landmark_names[:3])}")
            
            environment = visual_evidence.get("natural_environment", {})
            if environment:
                env_features = []
                for key, value in environment.items():
                    if value and value != "unknown":
                        env_features.append(f"{key}: {value}")
                if env_features:
                    reasoning_steps.append(f"3. 环境特征分析: {', '.join(env_features[:3])}")
            
            # 步骤2: 搜索验证阶段
            search_evidence = integrated_evidence.get("search_evidence", {})
            location_candidates = search_evidence.get("location_candidates", [])
            if location_candidates:
                reasoning_steps.append(f"4. 搜索验证: 通过多种搜索策略找到{len(location_candidates)}个位置候选")
            
            # 步骤3: 反思验证阶段
            reflection_evidence = integrated_evidence.get("reflection_evidence", {})
            consistency = reflection_evidence.get("consistency_assessment", {})
            if consistency.get("is_consistent"):
                reasoning_steps.append("5. 一致性验证: 各项证据相互支持，具有良好的一致性")
            else:
                reasoning_steps.append("5. 一致性验证: 发现部分证据存在不一致，需要谨慎评估")
            
            # 步骤4: 最终判断
            if primary_location.get("latitude") and primary_location.get("longitude"):
                reasoning_steps.append(f"6. 最终判断: 基于综合分析，确定最可能的位置为 {primary_location.get('name', '未知位置')}")
            else:
                reasoning_steps.append("6. 最终判断: 证据不足以确定具体的地理坐标，但可以推断大致区域")
            
            return "\n".join(reasoning_steps)
            
        except Exception as e:
            self.logger.error(f"构建推理过程失败: {str(e)}")
            return f"推理过程构建失败: {str(e)}"
    
    async def _evaluate_conclusion_confidence(
        self,
        primary_location: Dict[str, Any],
        integrated_evidence: Dict[str, Any],
        reflection_results: Dict[str, Any]
    ) -> float:
        """评估结论置信度"""
        
        try:
            confidence_factors = []
            
            # 位置置信度
            location_confidence = primary_location.get("confidence", 0.0)
            confidence_factors.append(location_confidence)
            
            # 证据总分
            total_evidence_score = integrated_evidence.get("total_evidence_score", 0.0)
            evidence_confidence = min(total_evidence_score / 3.0, 1.0)  # 归一化到0-1
            confidence_factors.append(evidence_confidence)
            
            # 反思调整后的置信度
            reflection_confidence = reflection_results.get("confidence_assessment", 0.0)
            confidence_factors.append(reflection_confidence)
            
            # 一致性因子
            consistency = reflection_results.get("consistency_check", {})
            consistency_score = consistency.get("consistency_score", 1.0)
            confidence_factors.append(consistency_score)
            
            # 证据充分性因子
            evidence_sufficiency = reflection_results.get("evidence_sufficiency", {})
            sufficiency_score = evidence_sufficiency.get("sufficiency_score", 0.0)
            confidence_factors.append(sufficiency_score)
            
            # 计算加权平均置信度
            weights = [0.3, 0.2, 0.2, 0.15, 0.15]  # 各因子的权重
            weighted_confidence = sum(
                factor * weight for factor, weight in zip(confidence_factors, weights)
            )
            
            # 应用疑点惩罚
            doubt_analysis = reflection_results.get("doubt_analysis", {})
            doubt_score = doubt_analysis.get("doubt_score", 0.0)
            doubt_penalty = 1.0 - (doubt_score * 0.3)  # 疑点最多降低30%置信度
            
            final_confidence = weighted_confidence * doubt_penalty
            
            return min(max(final_confidence, 0.0), 1.0)  # 确保在0-1范围内
            
        except Exception as e:
            self.logger.error(f"置信度评估失败: {str(e)}")
            return 0.0
    
    async def _identify_uncertainty_factors(
        self,
        integrated_evidence: Dict[str, Any],
        reflection_results: Dict[str, Any]
    ) -> List[str]:
        """识别不确定因素"""
        
        uncertainty_factors = []
        
        try:
            # 从反思结果中获取疑点
            doubt_analysis = reflection_results.get("doubt_analysis", {})
            doubts = doubt_analysis.get("doubts", [])
            uncertainty_factors.extend(doubts)
            
            # 检查证据不足的方面
            evidence_sufficiency = reflection_results.get("evidence_sufficiency", {})
            missing_evidence = evidence_sufficiency.get("missing_evidence", [])
            for evidence in missing_evidence:
                uncertainty_factors.append(f"缺少{evidence}")
            
            # 检查一致性问题
            consistency = reflection_results.get("consistency_check", {})
            inconsistencies = consistency.get("inconsistencies", [])
            uncertainty_factors.extend(inconsistencies)
            
            # 检查位置候选的分散性
            search_evidence = integrated_evidence.get("search_evidence", {})
            clustered_locations = search_evidence.get("clustered_locations", [])
            if len(clustered_locations) > 3:
                uncertainty_factors.append(f"存在{len(clustered_locations)}个不同的位置候选，增加了不确定性")
            
            # 检查文字线索的模糊性
            visual_evidence = integrated_evidence.get("visual_evidence", {})
            text_clues = visual_evidence.get("text_clues", [])
            ambiguous_texts = [text for text in text_clues if len(text) < 3]
            if ambiguous_texts:
                uncertainty_factors.append(f"存在{len(ambiguous_texts)}个模糊的文字线索")
            
            return uncertainty_factors[:5]  # 最多返回5个主要不确定因素
            
        except Exception as e:
            self.logger.error(f"识别不确定因素失败: {str(e)}")
            return [f"不确定因素识别失败: {str(e)}"]
    
    async def _generate_alternative_possibilities(
        self,
        integrated_evidence: Dict[str, Any],
        primary_location: Dict[str, Any]
    ) -> List[str]:
        """生成替代可能性"""
        
        alternatives = []
        
        try:
            # 从主要位置的替代候选中获取
            alternative_candidates = primary_location.get("alternative_candidates", [])
            for candidate in alternative_candidates[:3]:  # 最多3个
                name = candidate.get("name", "")
                confidence = candidate.get("confidence", 0.0)
                if name:
                    alternatives.append(f"{name} (置信度: {confidence:.2f})")
            
            # 从反思结果的替代假设中获取
            reflection_evidence = integrated_evidence.get("reflection_evidence", {})
            alternative_hypotheses = reflection_evidence.get("alternative_hypotheses", [])
            for hypothesis in alternative_hypotheses[:2]:  # 最多2个
                description = hypothesis.get("description", "")
                if description:
                    alternatives.append(description)
            
            # 基于环境特征生成区域性替代可能性
            visual_evidence = integrated_evidence.get("visual_evidence", {})
            environment = visual_evidence.get("natural_environment", {})
            climate = environment.get("climate", "")
            if climate and climate != "unknown":
                alternatives.append(f"具有{climate}气候特征的其他地区")
            
            return alternatives[:5]  # 最多返回5个替代可能性
            
        except Exception as e:
            self.logger.error(f"生成替代可能性失败: {str(e)}")
            return []
    
    async def _build_final_conclusion(
        self,
        primary_location: Dict[str, Any],
        reasoning_process: str,
        conclusion_confidence: float,
        integrated_evidence: Dict[str, Any],
        uncertainty_factors: List[str],
        alternative_possibilities: List[str]
    ) -> Dict[str, Any]:
        """构建最终结论"""
        
        try:
            # 构建位置信息
            final_location = {
                "country": self._extract_country_from_location(primary_location),
                "region": self._extract_region_from_location(primary_location),
                "city": self._extract_city_from_location(primary_location),
                "specific_location": primary_location.get("name", "位置未确定"),
                "coordinates": {
                    "lat": primary_location.get("latitude"),
                    "lng": primary_location.get("longitude")
                }
            }
            
            # 提取关键证据
            key_evidence = self._extract_key_evidence(integrated_evidence)
            
            # 构建最终结论
            final_conclusion = {
                "final_location": final_location,
                "confidence_score": conclusion_confidence,
                "reasoning_process": reasoning_process,
                "key_evidence": key_evidence,
                "uncertainty_factors": uncertainty_factors,
                "alternative_possibilities": alternative_possibilities,
                "analysis_metadata": {
                    "analysis_timestamp": datetime.now().isoformat(),
                    "total_evidence_sources": len(integrated_evidence),
                    "primary_location_source": primary_location.get("source", "unknown"),
                    "confidence_level": self._categorize_confidence(conclusion_confidence)
                }
            }
            
            return final_conclusion
            
        except Exception as e:
            self.logger.error(f"构建最终结论失败: {str(e)}")
            return {
                "final_location": {"specific_location": "结论构建失败"},
                "confidence_score": 0.0,
                "reasoning_process": f"结论构建失败: {str(e)}",
                "key_evidence": [],
                "uncertainty_factors": [str(e)],
                "alternative_possibilities": []
            }
    
    def _calculate_evidence_weights(
        self,
        visual_evidence: Dict[str, Any],
        search_evidence: Dict[str, Any],
        reflection_evidence: Dict[str, Any]
    ) -> Dict[str, float]:
        """计算证据权重"""
        
        weights = {}
        
        # 视觉证据权重
        text_weight = 0.3 if visual_evidence.get("text_clues") else 0.0
        landmark_weight = 0.4 if visual_evidence.get("landmarks") else 0.0
        environment_weight = 0.2 if visual_evidence.get("natural_environment") else 0.0
        
        weights["visual"] = text_weight + landmark_weight + environment_weight
        
        # 搜索证据权重
        location_weight = 0.5 if search_evidence.get("location_candidates") else 0.0
        verification_weight = 0.3 if search_evidence.get("verified_results") else 0.0
        
        weights["search"] = location_weight + verification_weight
        
        # 反思证据权重
        consistency_weight = 0.3 if reflection_evidence.get("consistency_assessment", {}).get("is_consistent") else 0.1
        sufficiency_weight = 0.2 if reflection_evidence.get("evidence_sufficiency", {}).get("is_sufficient") else 0.1
        
        weights["reflection"] = consistency_weight + sufficiency_weight
        
        return weights
    
    def _explain_location_selection(self, selected: Dict[str, Any], candidates: List[Dict[str, Any]]) -> str:
        """解释位置选择原因"""
        confidence = selected.get("confidence", 0.0)
        source = selected.get("source", "unknown")
        
        if confidence > 0.8:
            return f"高置信度选择 (置信度: {confidence:.2f})，来源: {source}"
        elif confidence > 0.6:
            return f"中等置信度选择 (置信度: {confidence:.2f})，来源: {source}"
        else:
            return f"低置信度选择 (置信度: {confidence:.2f})，来源: {source}，建议谨慎对待"
    
    def _extract_country_from_location(self, location: Dict[str, Any]) -> str:
        """从位置信息中提取国家"""
        # 简化实现，实际应用中可以使用地理编码服务
        name = location.get("name", "")
        if "china" in name.lower() or "中国" in name:
            return "中国"
        elif "usa" in name.lower() or "america" in name.lower():
            return "美国"
        else:
            return "未确定"
    
    def _extract_region_from_location(self, location: Dict[str, Any]) -> str:
        """从位置信息中提取地区"""
        # 简化实现
        return "未确定"
    
    def _extract_city_from_location(self, location: Dict[str, Any]) -> str:
        """从位置信息中提取城市"""
        # 简化实现
        name = location.get("name", "")
        return name if name else "未确定"
    
    def _extract_key_evidence(self, integrated_evidence: Dict[str, Any]) -> List[str]:
        """提取关键证据"""
        key_evidence = []
        
        visual_evidence = integrated_evidence.get("visual_evidence", {})
        
        # 文字证据
        text_clues = visual_evidence.get("text_clues", [])
        if text_clues:
            key_evidence.append(f"文字线索: {', '.join(text_clues[:3])}")
        
        # 地标证据
        landmarks = visual_evidence.get("landmarks", [])
        if landmarks:
            landmark_names = [str(lm) for lm in landmarks[:2]]
            key_evidence.append(f"地标识别: {', '.join(landmark_names)}")
        
        # 搜索验证证据
        search_evidence = integrated_evidence.get("search_evidence", {})
        location_candidates = search_evidence.get("location_candidates", [])
        if location_candidates:
            key_evidence.append(f"搜索验证: 找到{len(location_candidates)}个位置候选")
        
        return key_evidence
    
    def _categorize_confidence(self, confidence: float) -> str:
        """分类置信度等级"""
        if confidence >= 0.8:
            return "高"
        elif confidence >= 0.6:
            return "中"
        elif confidence >= 0.4:
            return "低"
        else:
            return "极低"
    
    def _validate_conclusion_quality(self, conclusion: Dict[str, Any]) -> bool:
        """验证结论质量"""
        # 检查必要字段
        required_fields = ["final_location", "confidence_score", "reasoning_process", "key_evidence"]
        
        for field in required_fields:
            if field not in conclusion:
                return False
        
        # 检查置信度
        confidence = conclusion.get("confidence_score", 0.0)
        if confidence < 0.3:  # 置信度过低
            return False
        
        # 检查关键证据
        key_evidence = conclusion.get("key_evidence", [])
        if len(key_evidence) < 1:  # 缺少关键证据
            return False
        
        return True
