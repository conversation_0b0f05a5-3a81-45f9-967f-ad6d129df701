"""
反思Actor
负责验证和优化分析结果，识别疑点和不一致性
"""

import json
from typing import Dict, Any, List
from datetime import datetime

from .base_actor import BaseActor
from ..core.context_manager import TaskResult, TaskStatus
from ..prompts import PromptTemplates
from ..utils import config


class ReflectionActor(BaseActor):
    """反思Actor"""
    
    def __init__(self, llm_client=None):
        super().__init__("reflection", llm_client)
    
    async def execute(self, task_input: Dict[str, Any]) -> TaskResult:
        """执行反思验证任务"""
        
        # 验证输入
        required_keys = ["analysis_results", "search_results"]
        if not self._validate_input(task_input, required_keys):
            return TaskResult(
                task_id="",
                actor_type=self.actor_type,
                status=TaskStatus.FAILED,
                result={},
                confidence=0.0,
                timestamp=datetime.now(),
                execution_time=0.0,
                error_message="缺少必需的输入参数"
            )
        
        analysis_results = task_input["analysis_results"]
        search_results = task_input["search_results"]
        current_conclusion = task_input.get("current_conclusion", {})
        verification_rounds = task_input.get("verification_rounds", 2)
        
        try:
            # 1. 一致性检查
            consistency_check = await self._check_consistency(analysis_results, search_results)
            
            # 2. 证据充分性评估
            evidence_assessment = await self._assess_evidence_sufficiency(
                analysis_results, search_results, current_conclusion
            )
            
            # 3. 识别潜在问题和疑点
            doubt_analysis = await self._identify_doubts_and_issues(
                analysis_results, search_results, current_conclusion
            )
            
            # 4. 生成替代假设
            alternative_hypotheses = await self._generate_alternative_hypotheses(
                analysis_results, search_results
            )
            
            # 5. 置信度重新评估
            confidence_reassessment = await self._reassess_confidence(
                analysis_results, search_results, consistency_check, evidence_assessment
            )
            
            # 6. 生成改进建议
            improvement_recommendations = await self._generate_improvement_recommendations(
                consistency_check, evidence_assessment, doubt_analysis
            )
            
            # 7. 整合反思结果
            reflection_result = self._integrate_reflection_results(
                consistency_check,
                evidence_assessment,
                doubt_analysis,
                alternative_hypotheses,
                confidence_reassessment,
                improvement_recommendations
            )
            
            # 计算反思置信度
            confidence = self._calculate_reflection_confidence(reflection_result)
            
            return TaskResult(
                task_id="",
                actor_type=self.actor_type,
                status=TaskStatus.COMPLETED,
                result=reflection_result,
                confidence=confidence,
                timestamp=datetime.now(),
                execution_time=0.0
            )
            
        except Exception as e:
            self.logger.error(f"反思验证任务失败: {str(e)}")
            return TaskResult(
                task_id="",
                actor_type=self.actor_type,
                status=TaskStatus.FAILED,
                result={},
                confidence=0.0,
                timestamp=datetime.now(),
                execution_time=0.0,
                error_message=str(e)
            )
    
    async def _check_consistency(
        self,
        analysis_results: Dict[str, Any],
        search_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """检查分析结果和搜索结果的一致性"""
        
        try:
            inconsistencies = []
            consistency_score = 1.0
            
            # 检查文字线索与搜索结果的一致性
            text_clues = analysis_results.get("text_clues", [])
            search_text_results = search_results.get("search_results", {}).get("verified_text_results", [])
            
            text_consistency = self._check_text_consistency(text_clues, search_text_results)
            if not text_consistency["is_consistent"]:
                inconsistencies.extend(text_consistency["issues"])
                consistency_score *= 0.8
            
            # 检查地标识别与搜索结果的一致性
            landmarks = analysis_results.get("landmarks", [])
            location_results = search_results.get("location_analysis", {}).get("location_candidates", [])
            
            landmark_consistency = self._check_landmark_consistency(landmarks, location_results)
            if not landmark_consistency["is_consistent"]:
                inconsistencies.extend(landmark_consistency["issues"])
                consistency_score *= 0.7
            
            # 检查环境特征与地理位置的一致性
            environment = analysis_results.get("natural_environment", {})
            recommended_locations = search_results.get("recommended_locations", [])
            
            environment_consistency = self._check_environment_consistency(environment, recommended_locations)
            if not environment_consistency["is_consistent"]:
                inconsistencies.extend(environment_consistency["issues"])
                consistency_score *= 0.9
            
            return {
                "is_consistent": len(inconsistencies) == 0,
                "consistency_score": consistency_score,
                "inconsistencies": inconsistencies,
                "detailed_checks": {
                    "text_consistency": text_consistency,
                    "landmark_consistency": landmark_consistency,
                    "environment_consistency": environment_consistency
                }
            }
            
        except Exception as e:
            self.logger.error(f"一致性检查失败: {str(e)}")
            return {
                "is_consistent": False,
                "consistency_score": 0.0,
                "inconsistencies": [f"一致性检查失败: {str(e)}"],
                "detailed_checks": {}
            }
    
    async def _assess_evidence_sufficiency(
        self,
        analysis_results: Dict[str, Any],
        search_results: Dict[str, Any],
        current_conclusion: Dict[str, Any]
    ) -> Dict[str, Any]:
        """评估证据充分性"""
        
        try:
            evidence_types = []
            missing_evidence = []
            sufficiency_score = 0.0
            
            # 检查文字证据
            text_clues = analysis_results.get("text_clues", [])
            if text_clues:
                evidence_types.append("textual")
                sufficiency_score += 0.3
            else:
                missing_evidence.append("文字线索")
            
            # 检查视觉证据
            landmarks = analysis_results.get("landmarks", [])
            architectural_features = analysis_results.get("architectural_features", [])
            if landmarks or architectural_features:
                evidence_types.append("visual")
                sufficiency_score += 0.3
            else:
                missing_evidence.append("视觉特征")
            
            # 检查地理证据
            location_candidates = search_results.get("location_analysis", {}).get("location_candidates", [])
            if location_candidates:
                evidence_types.append("geographical")
                sufficiency_score += 0.2
            else:
                missing_evidence.append("地理位置信息")
            
            # 检查搜索验证证据
            verified_results = search_results.get("search_results", {})
            total_verified = sum(len(results) for results in verified_results.values())
            if total_verified > 0:
                evidence_types.append("search_verification")
                sufficiency_score += 0.2
            else:
                missing_evidence.append("搜索验证")
            
            # 评估证据质量
            evidence_quality = self._assess_evidence_quality(analysis_results, search_results)
            sufficiency_score *= evidence_quality
            
            is_sufficient = sufficiency_score >= config.agent.confidence_threshold
            
            return {
                "is_sufficient": is_sufficient,
                "sufficiency_score": sufficiency_score,
                "evidence_types": evidence_types,
                "missing_evidence": missing_evidence,
                "evidence_quality": evidence_quality,
                "required_threshold": config.agent.confidence_threshold
            }
            
        except Exception as e:
            self.logger.error(f"证据充分性评估失败: {str(e)}")
            return {
                "is_sufficient": False,
                "sufficiency_score": 0.0,
                "evidence_types": [],
                "missing_evidence": ["评估失败"],
                "evidence_quality": 0.0
            }
    
    async def _identify_doubts_and_issues(
        self,
        analysis_results: Dict[str, Any],
        search_results: Dict[str, Any],
        current_conclusion: Dict[str, Any]
    ) -> Dict[str, Any]:
        """识别疑点和问题"""
        
        try:
            doubts = []
            issues = []
            doubt_score = 0.0
            
            # 检查分析结果中的低置信度项目
            confidence_scores = analysis_results.get("confidence_scores", {})
            for item, confidence in confidence_scores.items():
                if isinstance(confidence, (int, float)) and confidence < 0.5:
                    doubts.append(f"{item}的置信度较低: {confidence}")
                    doubt_score += 0.1
            
            # 检查搜索结果的稀少性
            search_summary = search_results.get("search_summary", {})
            total_results = search_summary.get("total_results", 0)
            if total_results < 3:
                issues.append(f"搜索结果过少: 仅{total_results}个")
                doubt_score += 0.2
            
            # 检查位置候选的分散性
            location_candidates = search_results.get("location_analysis", {}).get("location_candidates", [])
            if len(location_candidates) > 1:
                location_spread = self._calculate_location_spread(location_candidates)
                if location_spread > 100:  # 如果候选位置分散超过100公里
                    doubts.append(f"候选位置过于分散: {location_spread:.1f}公里")
                    doubt_score += 0.15
            
            # 检查文字线索的模糊性
            text_clues = analysis_results.get("text_clues", [])
            ambiguous_texts = [text for text in text_clues if len(text) < 3 or not text.isalnum()]
            if ambiguous_texts:
                doubts.append(f"存在模糊的文字线索: {len(ambiguous_texts)}个")
                doubt_score += 0.1
            
            # 使用LLM进行深度疑点分析
            if self.llm_client:
                llm_doubts = await self._llm_doubt_analysis(analysis_results, search_results)
                doubts.extend(llm_doubts)
            
            return {
                "has_doubts": len(doubts) > 0 or len(issues) > 0,
                "doubt_score": min(doubt_score, 1.0),
                "doubts": doubts,
                "issues": issues,
                "doubt_threshold": 0.3
            }
            
        except Exception as e:
            self.logger.error(f"疑点识别失败: {str(e)}")
            return {
                "has_doubts": True,
                "doubt_score": 1.0,
                "doubts": [f"疑点识别失败: {str(e)}"],
                "issues": [],
                "doubt_threshold": 0.3
            }
    
    async def _generate_alternative_hypotheses(
        self,
        analysis_results: Dict[str, Any],
        search_results: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """生成替代假设"""
        
        try:
            alternatives = []
            
            # 基于搜索结果生成替代位置
            location_candidates = search_results.get("location_analysis", {}).get("location_candidates", [])
            clustered_locations = search_results.get("location_analysis", {}).get("clustered_locations", [])
            
            for i, cluster in enumerate(clustered_locations[1:4]):  # 取前3个替代选项
                alternative = {
                    "hypothesis_id": f"alt_{i+1}",
                    "type": "alternative_location",
                    "description": f"替代位置假设 {i+1}",
                    "location": cluster.get("center", {}),
                    "confidence": cluster.get("confidence", 0.0),
                    "supporting_evidence": [member.get("name", "") for member in cluster.get("members", [])]
                }
                alternatives.append(alternative)
            
            # 基于文字线索的不同解释生成假设
            text_clues = analysis_results.get("text_clues", [])
            geographic_texts = analysis_results.get("geographic_texts", [])
            
            for text in geographic_texts[:2]:  # 取前2个地理文字
                if text and len(text) > 3:
                    alternative = {
                        "hypothesis_id": f"text_alt_{len(alternatives)+1}",
                        "type": "text_interpretation",
                        "description": f"基于文字'{text}'的替代解释",
                        "text_clue": text,
                        "confidence": 0.4,
                        "supporting_evidence": [f"文字线索: {text}"]
                    }
                    alternatives.append(alternative)
            
            # 基于环境特征生成区域性假设
            environment = analysis_results.get("natural_environment", {})
            if environment:
                climate = environment.get("climate", "")
                vegetation = environment.get("vegetation", "")
                
                if climate and climate != "unknown":
                    alternative = {
                        "hypothesis_id": f"env_alt_{len(alternatives)+1}",
                        "type": "environmental_region",
                        "description": f"基于{climate}气候的区域假设",
                        "climate": climate,
                        "vegetation": vegetation,
                        "confidence": 0.3,
                        "supporting_evidence": [f"气候特征: {climate}", f"植被类型: {vegetation}"]
                    }
                    alternatives.append(alternative)
            
            return alternatives
            
        except Exception as e:
            self.logger.error(f"生成替代假设失败: {str(e)}")
            return []
    
    async def _reassess_confidence(
        self,
        analysis_results: Dict[str, Any],
        search_results: Dict[str, Any],
        consistency_check: Dict[str, Any],
        evidence_assessment: Dict[str, Any]
    ) -> Dict[str, Any]:
        """重新评估置信度"""
        
        try:
            # 获取原始置信度
            original_confidence = analysis_results.get("confidence_scores", {})
            search_confidence = search_results.get("search_summary", {}).get("confidence", 0.5)
            
            # 基于一致性调整置信度
            consistency_factor = consistency_check.get("consistency_score", 1.0)
            
            # 基于证据充分性调整置信度
            evidence_factor = evidence_assessment.get("sufficiency_score", 0.5)
            
            # 计算调整后的置信度
            adjusted_confidence = {}
            for key, value in original_confidence.items():
                if isinstance(value, (int, float)):
                    adjusted = value * consistency_factor * evidence_factor
                    adjusted_confidence[key] = min(adjusted, 1.0)
                else:
                    adjusted_confidence[key] = value
            
            # 计算整体置信度
            overall_confidence = (
                sum(adjusted_confidence.values()) / len(adjusted_confidence) 
                if adjusted_confidence else 0.0
            )
            
            # 应用搜索结果的影响
            final_confidence = (overall_confidence + search_confidence) / 2
            
            return {
                "original_confidence": original_confidence,
                "adjusted_confidence": adjusted_confidence,
                "overall_confidence": overall_confidence,
                "final_confidence": final_confidence,
                "adjustment_factors": {
                    "consistency_factor": consistency_factor,
                    "evidence_factor": evidence_factor
                }
            }
            
        except Exception as e:
            self.logger.error(f"置信度重新评估失败: {str(e)}")
            return {
                "original_confidence": {},
                "adjusted_confidence": {},
                "overall_confidence": 0.0,
                "final_confidence": 0.0,
                "adjustment_factors": {}
            }
    
    async def _generate_improvement_recommendations(
        self,
        consistency_check: Dict[str, Any],
        evidence_assessment: Dict[str, Any],
        doubt_analysis: Dict[str, Any]
    ) -> List[str]:
        """生成改进建议"""
        
        recommendations = []
        
        try:
            # 基于一致性问题生成建议
            if not consistency_check.get("is_consistent", True):
                recommendations.append("需要解决分析结果与搜索结果之间的不一致性")
                inconsistencies = consistency_check.get("inconsistencies", [])
                for inconsistency in inconsistencies[:3]:  # 最多显示3个
                    recommendations.append(f"- 解决: {inconsistency}")
            
            # 基于证据不足生成建议
            if not evidence_assessment.get("is_sufficient", True):
                missing_evidence = evidence_assessment.get("missing_evidence", [])
                for evidence in missing_evidence:
                    recommendations.append(f"需要补充{evidence}")
            
            # 基于疑点生成建议
            if doubt_analysis.get("has_doubts", False):
                recommendations.append("需要进一步验证以下疑点:")
                doubts = doubt_analysis.get("doubts", [])
                for doubt in doubts[:3]:  # 最多显示3个
                    recommendations.append(f"- {doubt}")
            
            # 通用改进建议
            if not recommendations:
                recommendations.append("当前分析结果较为可靠，建议进行最终结论整合")
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"生成改进建议失败: {str(e)}")
            return ["建议重新进行分析"]
    
    def _integrate_reflection_results(
        self,
        consistency_check: Dict[str, Any],
        evidence_assessment: Dict[str, Any],
        doubt_analysis: Dict[str, Any],
        alternative_hypotheses: List[Dict[str, Any]],
        confidence_reassessment: Dict[str, Any],
        improvement_recommendations: List[str]
    ) -> Dict[str, Any]:
        """整合反思结果"""
        
        # 判断是否需要额外分析
        requires_additional_analysis = (
            not consistency_check.get("is_consistent", True) or
            not evidence_assessment.get("is_sufficient", True) or
            doubt_analysis.get("doubt_score", 0) > doubt_analysis.get("doubt_threshold", 0.3)
        )
        
        return {
            "consistency_check": consistency_check,
            "evidence_sufficiency": evidence_assessment,
            "doubt_analysis": doubt_analysis,
            "alternative_hypotheses": alternative_hypotheses,
            "confidence_assessment": confidence_reassessment.get("final_confidence", 0.0),
            "recommendations": improvement_recommendations,
            "requires_additional_analysis": requires_additional_analysis,
            "reflection_summary": {
                "is_consistent": consistency_check.get("is_consistent", True),
                "is_sufficient": evidence_assessment.get("is_sufficient", True),
                "has_doubts": doubt_analysis.get("has_doubts", False),
                "alternative_count": len(alternative_hypotheses),
                "final_confidence": confidence_reassessment.get("final_confidence", 0.0)
            }
        }
    
    def _calculate_reflection_confidence(self, reflection_result: Dict[str, Any]) -> float:
        """计算反思置信度"""
        summary = reflection_result.get("reflection_summary", {})
        
        confidence_factors = []
        
        # 一致性因子
        if summary.get("is_consistent", True):
            confidence_factors.append(0.9)
        else:
            confidence_factors.append(0.3)
        
        # 证据充分性因子
        if summary.get("is_sufficient", True):
            confidence_factors.append(0.8)
        else:
            confidence_factors.append(0.4)
        
        # 疑点因子
        if not summary.get("has_doubts", False):
            confidence_factors.append(0.8)
        else:
            confidence_factors.append(0.5)
        
        # 最终置信度因子
        final_confidence = summary.get("final_confidence", 0.0)
        confidence_factors.append(final_confidence)
        
        return sum(confidence_factors) / len(confidence_factors)
    
    # 辅助方法
    def _check_text_consistency(self, text_clues: List[str], search_results: List[Dict]) -> Dict[str, Any]:
        """检查文字线索一致性"""
        # 简化实现
        return {"is_consistent": True, "issues": []}
    
    def _check_landmark_consistency(self, landmarks: List, location_results: List[Dict]) -> Dict[str, Any]:
        """检查地标一致性"""
        # 简化实现
        return {"is_consistent": True, "issues": []}
    
    def _check_environment_consistency(self, environment: Dict, locations: List[Dict]) -> Dict[str, Any]:
        """检查环境一致性"""
        # 简化实现
        return {"is_consistent": True, "issues": []}
    
    def _assess_evidence_quality(self, analysis_results: Dict, search_results: Dict) -> float:
        """评估证据质量"""
        # 简化实现
        return 0.8
    
    def _calculate_location_spread(self, locations: List[Dict]) -> float:
        """计算位置分散度"""
        # 简化实现，返回模拟的分散度
        return 50.0
    
    async def _llm_doubt_analysis(self, analysis_results: Dict, search_results: Dict) -> List[str]:
        """使用LLM进行疑点分析"""
        try:
            prompt = PromptTemplates.get_prompt(
                "reflection",
                analysis_results=json.dumps(analysis_results, ensure_ascii=False),
                search_results=json.dumps(search_results, ensure_ascii=False),
                current_conclusion="{}"
            )

            response = await self._call_llm(prompt)

            # 解析LLM响应中的疑点
            doubts = []
            if response:
                # 尝试解析JSON格式的响应
                try:
                    if "{" in response and "}" in response:
                        start = response.find("{")
                        end = response.rfind("}") + 1
                        json_str = response[start:end]
                        parsed_response = json.loads(json_str)

                        # 提取疑点信息
                        if "doubts" in parsed_response:
                            doubts.extend(parsed_response["doubts"])
                        if "issues" in parsed_response:
                            doubts.extend(parsed_response["issues"])
                        if "inconsistencies" in parsed_response:
                            doubts.extend(parsed_response["inconsistencies"])

                except json.JSONDecodeError:
                    # 如果不是JSON格式，尝试从文本中提取疑点
                    lines = response.split('\n')
                    for line in lines:
                        line = line.strip()
                        if any(keyword in line.lower() for keyword in ['疑点', '问题', '不一致', 'doubt', 'issue', 'inconsistent']):
                            if len(line) > 10:  # 过滤太短的行
                                doubts.append(line)

            return doubts[:5]  # 最多返回5个疑点

        except Exception as e:
            self.logger.error(f"LLM疑点分析失败: {str(e)}")
            return []
