"""
日志系统模块
提供结构化日志记录功能
"""

import sys
from pathlib import Path
from loguru import logger
from typing import Optional, Dict, Any
from .config import config


class Logger:
    """日志管理器"""
    
    def __init__(self):
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志配置"""
        # 移除默认处理器
        logger.remove()
        
        # 控制台输出
        logger.add(
            sys.stdout,
            format=config.logging.format,
            level=config.logging.level,
            colorize=True
        )
        
        # 文件输出
        log_path = Path(config.logging.file_path)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            log_path,
            format=config.logging.format,
            level=config.logging.level,
            rotation=config.logging.rotation,
            retention=config.logging.retention,
            encoding="utf-8"
        )
    
    def info(self, message: str, **kwargs):
        """记录信息日志"""
        logger.bind(**kwargs).info(message)
    
    def debug(self, message: str, **kwargs):
        """记录调试日志"""
        logger.bind(**kwargs).debug(message)
    
    def warning(self, message: str, **kwargs):
        """记录警告日志"""
        logger.bind(**kwargs).warning(message)
    
    def error(self, message: str, **kwargs):
        """记录错误日志"""
        logger.bind(**kwargs).error(message)
    
    def critical(self, message: str, **kwargs):
        """记录严重错误日志"""
        logger.bind(**kwargs).critical(message)
    
    def log_agent_action(self, agent_id: str, action: str, details: Dict[str, Any]):
        """记录Agent行为日志"""
        self.info(
            f"Agent行为: {action}",
            agent_id=agent_id,
            action=action,
            details=details
        )
    
    def log_actor_execution(self, actor_type: str, task: str, result: Dict[str, Any]):
        """记录Actor执行日志"""
        self.info(
            f"Actor执行: {actor_type} - {task}",
            actor_type=actor_type,
            task=task,
            result=result
        )
    
    def log_tool_usage(self, tool_name: str, input_data: Dict[str, Any], output_data: Dict[str, Any]):
        """记录工具使用日志"""
        self.info(
            f"工具使用: {tool_name}",
            tool_name=tool_name,
            input_data=input_data,
            output_data=output_data
        )
    
    def log_error_with_context(self, error: Exception, context: Dict[str, Any]):
        """记录带上下文的错误日志"""
        self.error(
            f"错误发生: {str(error)}",
            error_type=type(error).__name__,
            error_message=str(error),
            context=context
        )


# 全局日志实例
app_logger = Logger()
