"""
上下文管理器
负责管理Agent执行过程中的状态和上下文信息
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class TaskResult:
    """任务结果数据类"""
    task_id: str
    actor_type: str
    status: TaskStatus
    result: Dict[str, Any]
    confidence: float
    timestamp: datetime
    execution_time: float
    error_message: Optional[str] = None


@dataclass
class AnalysisContext:
    """分析上下文数据类"""
    image_path: str
    user_requirements: str
    image_description: str
    extracted_clues: Dict[str, Any]
    search_results: List[Dict[str, Any]]
    reflection_results: Dict[str, Any]
    final_conclusion: Optional[Dict[str, Any]]
    confidence_history: List[float]
    iteration_count: int


class ContextManager:
    """上下文管理器"""
    
    def __init__(self):
        self.current_context: Optional[AnalysisContext] = None
        self.task_history: List[TaskResult] = []
        self.session_id: str = self._generate_session_id()
        self.start_time: datetime = datetime.now()
    
    def _generate_session_id(self) -> str:
        """生成会话ID"""
        return f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    def initialize_context(
        self,
        image_path: str,
        user_requirements: str,
        image_description: str
    ) -> None:
        """初始化分析上下文"""
        self.current_context = AnalysisContext(
            image_path=image_path,
            user_requirements=user_requirements,
            image_description=image_description,
            extracted_clues={},
            search_results=[],
            reflection_results={},
            final_conclusion=None,
            confidence_history=[],
            iteration_count=0
        )
    
    def add_task_result(self, task_result: TaskResult) -> None:
        """添加任务结果"""
        self.task_history.append(task_result)
        
        # 更新上下文中的相关信息
        if self.current_context:
            if task_result.actor_type == "image_analysis":
                self.current_context.extracted_clues = task_result.result
            elif task_result.actor_type == "search":
                self.current_context.search_results.append(task_result.result)
            elif task_result.actor_type == "reflection":
                self.current_context.reflection_results = task_result.result
            elif task_result.actor_type == "conclusion":
                self.current_context.final_conclusion = task_result.result
            
            # 记录置信度历史
            self.current_context.confidence_history.append(task_result.confidence)
    
    def get_current_state(self) -> Dict[str, Any]:
        """获取当前状态"""
        if not self.current_context:
            return {}
        
        return {
            "context": asdict(self.current_context),
            "recent_tasks": [asdict(task) for task in self.task_history[-5:]],
            "session_info": {
                "session_id": self.session_id,
                "start_time": self.start_time.isoformat(),
                "duration": (datetime.now() - self.start_time).total_seconds()
            }
        }
    
    def get_confidence_trend(self) -> List[float]:
        """获取置信度趋势"""
        if not self.current_context:
            return []
        return self.current_context.confidence_history
    
    def should_continue_analysis(self, max_iterations: int, min_confidence: float) -> bool:
        """判断是否应该继续分析"""
        if not self.current_context:
            return False
        
        # 检查迭代次数
        if self.current_context.iteration_count >= max_iterations:
            return False
        
        # 检查置信度
        if (self.current_context.confidence_history and 
            self.current_context.confidence_history[-1] >= min_confidence):
            return False
        
        return True
    
    def increment_iteration(self) -> None:
        """增加迭代计数"""
        if self.current_context:
            self.current_context.iteration_count += 1
    
    def get_analysis_summary(self) -> Dict[str, Any]:
        """获取分析摘要"""
        if not self.current_context:
            return {}
        
        successful_tasks = [task for task in self.task_history 
                          if task.status == TaskStatus.COMPLETED]
        failed_tasks = [task for task in self.task_history 
                       if task.status == TaskStatus.FAILED]
        
        return {
            "total_tasks": len(self.task_history),
            "successful_tasks": len(successful_tasks),
            "failed_tasks": len(failed_tasks),
            "iterations": self.current_context.iteration_count,
            "final_confidence": (self.current_context.confidence_history[-1] 
                               if self.current_context.confidence_history else 0.0),
            "has_conclusion": self.current_context.final_conclusion is not None,
            "execution_time": (datetime.now() - self.start_time).total_seconds()
        }
    
    def export_context(self) -> str:
        """导出上下文为JSON字符串"""
        state = self.get_current_state()
        return json.dumps(state, ensure_ascii=False, indent=2, default=str)
    
    def clear_context(self) -> None:
        """清空上下文"""
        self.current_context = None
        self.task_history.clear()
        self.session_id = self._generate_session_id()
        self.start_time = datetime.now()
