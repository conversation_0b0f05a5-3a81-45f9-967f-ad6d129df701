"""
主Agent类
实现PlanAct框架，协调各个Actor执行图片地理位置识别任务
"""

import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime

from .planner import Planner
from .context_manager import ContextManager, TaskResult, TaskStatus
from ..actors.image_analysis_actor import ImageAnalysisActor
from ..actors.search_actor import SearchActor
from ..actors.reflection_actor import ReflectionActor
from ..actors.conclusion_actor import ConclusionActor
from ..utils import app_logger, config


class ImageLocationAgent:
    """图片地理位置识别Agent"""
    
    def __init__(self, llm_client=None):
        self.llm_client = llm_client
        self.logger = app_logger
        
        # 初始化核心组件
        self.planner = Planner(llm_client)
        self.context_manager = ContextManager()
        
        # 初始化Actor
        self.actors = {
            "image_analysis": ImageAnalysisActor(llm_client),
            "search": SearchActor(llm_client),
            "reflection": ReflectionActor(llm_client),
            "conclusion": ConclusionActor(llm_client)
        }
        
        # Agent配置
        self.max_iterations = config.agent.max_iterations
        self.confidence_threshold = config.agent.confidence_threshold
        self.enable_reflection = config.agent.enable_reflection
        self.enable_multi_round = config.agent.enable_multi_round_analysis
    
    async def analyze_image_location(
        self,
        image_path: str,
        user_requirements: str = "识别图片拍摄地点",
        image_description: str = ""
    ) -> Dict[str, Any]:
        """分析图片地理位置的主要方法"""
        
        self.logger.info("开始图片地理位置分析", 
                        image_path=image_path,
                        user_requirements=user_requirements)
        
        try:
            # 1. 初始化分析上下文
            if not image_description:
                image_description = await self._generate_basic_image_description(image_path)
            
            self.context_manager.initialize_context(
                image_path=image_path,
                user_requirements=user_requirements,
                image_description=image_description
            )
            
            # 2. 创建执行计划
            execution_plan = await self.planner.create_plan(
                image_description=image_description,
                user_requirements=user_requirements,
                context=self.context_manager.get_current_state()
            )
            
            self.logger.info("执行计划创建完成", 
                           plan_id=execution_plan.plan_id,
                           steps_count=len(execution_plan.steps))
            
            # 3. 执行分析流程
            final_result = await self._execute_analysis_workflow(execution_plan, image_path)
            
            # 4. 生成分析报告
            analysis_report = await self._generate_analysis_report(final_result)
            
            self.logger.info("图片地理位置分析完成", 
                           confidence=final_result.get("confidence_score", 0.0),
                           location=final_result.get("final_location", {}).get("specific_location", "未确定"))
            
            return analysis_report
            
        except Exception as e:
            self.logger.error_with_context(e, {
                "image_path": image_path,
                "user_requirements": user_requirements
            })
            
            return {
                "success": False,
                "error": str(e),
                "final_location": {"specific_location": "分析失败"},
                "confidence_score": 0.0,
                "analysis_summary": self.context_manager.get_analysis_summary()
            }
    
    async def _execute_analysis_workflow(
        self,
        execution_plan,
        image_path: str
    ) -> Dict[str, Any]:
        """执行分析工作流程"""
        
        analysis_results = {}
        search_results = {}
        reflection_results = {}
        final_conclusion = {}
        
        iteration = 0
        
        while (iteration < self.max_iterations and 
               self.context_manager.should_continue_analysis(self.max_iterations, self.confidence_threshold)):
            
            iteration += 1
            self.context_manager.increment_iteration()
            
            self.logger.info(f"开始第 {iteration} 轮分析")
            
            try:
                # 阶段1: 图片分析
                if not analysis_results or iteration > 1:
                    analysis_results = await self._execute_image_analysis(image_path, iteration)
                
                # 阶段2: 搜索调查
                search_results = await self._execute_search_investigation(
                    analysis_results, image_path, iteration
                )
                
                # 阶段3: 反思验证（如果启用）
                if self.enable_reflection:
                    reflection_results = await self._execute_reflection_verification(
                        analysis_results, search_results, iteration
                    )
                
                # 阶段4: 结论整合
                final_conclusion = await self._execute_conclusion_integration(
                    analysis_results, search_results, reflection_results, iteration
                )
                
                # 检查是否达到置信度要求
                current_confidence = final_conclusion.get("confidence_score", 0.0)
                if current_confidence >= self.confidence_threshold:
                    self.logger.info(f"达到置信度要求 ({current_confidence:.2f} >= {self.confidence_threshold})，结束分析")
                    break
                
                # 如果不启用多轮分析，只执行一次
                if not self.enable_multi_round:
                    break
                
                # 根据当前结果调整计划
                if iteration < self.max_iterations:
                    execution_plan = self.planner.adjust_plan(
                        execution_plan,
                        [analysis_results, search_results, reflection_results],
                        self.context_manager.get_current_state()
                    )
                
            except Exception as e:
                self.logger.error(f"第 {iteration} 轮分析失败: {str(e)}")
                if iteration == 1:  # 如果第一轮就失败，直接抛出异常
                    raise
                break  # 其他轮次失败，使用之前的结果
        
        return final_conclusion
    
    async def _execute_image_analysis(self, image_path: str, iteration: int) -> Dict[str, Any]:
        """执行图片分析阶段"""
        
        self.logger.info(f"执行图片分析 (第 {iteration} 轮)")
        
        task_input = {
            "image_path": image_path,
            "detail_level": "ultra_high" if iteration > 1 else "high"
        }
        
        result = await self.actors["image_analysis"].execute_with_retry(task_input)
        self.context_manager.add_task_result(result)
        
        if result.status == TaskStatus.COMPLETED:
            self.logger.info("图片分析完成", 
                           confidence=result.confidence,
                           text_clues_count=len(result.result.get("text_clues", [])))
            return result.result
        else:
            raise Exception(f"图片分析失败: {result.error_message}")
    
    async def _execute_search_investigation(
        self,
        analysis_results: Dict[str, Any],
        image_path: str,
        iteration: int
    ) -> Dict[str, Any]:
        """执行搜索调查阶段"""
        
        self.logger.info(f"执行搜索调查 (第 {iteration} 轮)")
        
        # 根据迭代次数调整搜索策略
        search_strategies = ["text_search", "reverse_image", "location_search"]
        if iteration > 1:
            search_strategies.append("image_search")
        
        task_input = {
            "clues": analysis_results,
            "image_path": image_path,
            "search_strategies": search_strategies
        }
        
        result = await self.actors["search"].execute_with_retry(task_input)
        self.context_manager.add_task_result(result)
        
        if result.status == TaskStatus.COMPLETED:
            self.logger.info("搜索调查完成", 
                           confidence=result.confidence,
                           candidates_count=len(result.result.get("location_analysis", {}).get("location_candidates", [])))
            return result.result
        else:
            self.logger.warning(f"搜索调查失败: {result.error_message}")
            return {}  # 搜索失败不阻断流程
    
    async def _execute_reflection_verification(
        self,
        analysis_results: Dict[str, Any],
        search_results: Dict[str, Any],
        iteration: int
    ) -> Dict[str, Any]:
        """执行反思验证阶段"""
        
        self.logger.info(f"执行反思验证 (第 {iteration} 轮)")
        
        task_input = {
            "analysis_results": analysis_results,
            "search_results": search_results,
            "current_conclusion": {},
            "verification_rounds": 2 if iteration == 1 else 1
        }
        
        result = await self.actors["reflection"].execute_with_retry(task_input)
        self.context_manager.add_task_result(result)
        
        if result.status == TaskStatus.COMPLETED:
            self.logger.info("反思验证完成", 
                           confidence=result.confidence,
                           requires_additional=result.result.get("requires_additional_analysis", False))
            return result.result
        else:
            self.logger.warning(f"反思验证失败: {result.error_message}")
            return {}  # 反思失败不阻断流程
    
    async def _execute_conclusion_integration(
        self,
        analysis_results: Dict[str, Any],
        search_results: Dict[str, Any],
        reflection_results: Dict[str, Any],
        iteration: int
    ) -> Dict[str, Any]:
        """执行结论整合阶段"""
        
        self.logger.info(f"执行结论整合 (第 {iteration} 轮)")
        
        task_input = {
            "image_analysis": analysis_results,
            "search_results": search_results,
            "reflection_results": reflection_results,
            "require_evidence": True
        }
        
        result = await self.actors["conclusion"].execute_with_retry(task_input)
        self.context_manager.add_task_result(result)
        
        if result.status == TaskStatus.COMPLETED:
            self.logger.info("结论整合完成", 
                           confidence=result.confidence,
                           location=result.result.get("final_location", {}).get("specific_location", "未确定"))
            return result.result
        else:
            raise Exception(f"结论整合失败: {result.error_message}")
    
    async def _generate_basic_image_description(self, image_path: str) -> str:
        """生成基础图片描述"""
        try:
            # 这里可以使用简单的图片分析或者用户输入
            # 暂时返回基础描述
            return f"待分析的图片: {image_path}"
        except Exception as e:
            self.logger.warning(f"生成图片描述失败: {str(e)}")
            return "图片描述生成失败"
    
    async def _generate_analysis_report(self, final_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成分析报告"""
        
        analysis_summary = self.context_manager.get_analysis_summary()
        context_state = self.context_manager.get_current_state()
        
        report = {
            "success": True,
            "final_location": final_result.get("final_location", {}),
            "confidence_score": final_result.get("confidence_score", 0.0),
            "reasoning_process": final_result.get("reasoning_process", ""),
            "key_evidence": final_result.get("key_evidence", []),
            "uncertainty_factors": final_result.get("uncertainty_factors", []),
            "alternative_possibilities": final_result.get("alternative_possibilities", []),
            "analysis_metadata": final_result.get("analysis_metadata", {}),
            "analysis_summary": analysis_summary,
            "execution_details": {
                "total_iterations": analysis_summary.get("iterations", 0),
                "execution_time": analysis_summary.get("execution_time", 0.0),
                "successful_tasks": analysis_summary.get("successful_tasks", 0),
                "failed_tasks": analysis_summary.get("failed_tasks", 0)
            },
            "confidence_trend": self.context_manager.get_confidence_trend(),
            "session_id": context_state.get("session_info", {}).get("session_id", "")
        }
        
        return report
    
    def get_actor_stats(self) -> Dict[str, Any]:
        """获取Actor统计信息"""
        stats = {}
        for name, actor in self.actors.items():
            stats[name] = actor.get_stats()
        return stats
    
    def reset_context(self) -> None:
        """重置分析上下文"""
        self.context_manager.clear_context()
        self.logger.info("分析上下文已重置")
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_status = {
            "agent_status": "healthy",
            "llm_client_available": self.llm_client is not None,
            "actors_status": {},
            "configuration": {
                "max_iterations": self.max_iterations,
                "confidence_threshold": self.confidence_threshold,
                "enable_reflection": self.enable_reflection,
                "enable_multi_round": self.enable_multi_round
            }
        }
        
        # 检查各个Actor的状态
        for name, actor in self.actors.items():
            try:
                stats = actor.get_stats()
                health_status["actors_status"][name] = "healthy"
            except Exception as e:
                health_status["actors_status"][name] = f"error: {str(e)}"
        
        return health_status
