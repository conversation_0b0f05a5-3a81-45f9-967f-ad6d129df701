"""
规划器模块
负责分析任务并制定执行计划
"""

import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

from ..utils import app_logger, config
from ..prompts import PromptTemplates


class PlanStage(Enum):
    """计划阶段枚举"""
    CLUE_EXTRACTION = "clue_extraction"
    INVESTIGATION = "investigation"
    REFLECTION = "reflection"
    CONCLUSION = "conclusion"


@dataclass
class PlanStep:
    """计划步骤"""
    step_id: str
    stage: PlanStage
    actor_type: str
    description: str
    priority: int
    dependencies: List[str]
    expected_confidence: float
    parameters: Dict[str, Any]


@dataclass
class ExecutionPlan:
    """执行计划"""
    plan_id: str
    steps: List[PlanStep]
    estimated_duration: float
    success_criteria: Dict[str, Any]
    fallback_strategies: List[str]


class Planner:
    """规划器类"""
    
    def __init__(self, llm_client):
        self.llm_client = llm_client
        self.logger = app_logger
    
    async def create_plan(
        self,
        image_description: str,
        user_requirements: str,
        context: Optional[Dict[str, Any]] = None
    ) -> ExecutionPlan:
        """创建执行计划"""
        
        self.logger.info("开始创建执行计划", 
                        image_desc_length=len(image_description),
                        user_req_length=len(user_requirements))
        
        try:
            # 生成规划Prompt
            prompt = PromptTemplates.get_prompt(
                "planner",
                image_description=image_description,
                user_requirements=user_requirements
            )
            
            # 调用LLM生成计划
            plan_response = await self._call_llm_for_planning(prompt)
            
            # 解析计划响应
            execution_plan = self._parse_plan_response(plan_response)
            
            self.logger.info("执行计划创建完成", 
                           plan_id=execution_plan.plan_id,
                           steps_count=len(execution_plan.steps))
            
            return execution_plan
            
        except Exception as e:
            self.logger.error_with_context(e, {
                "image_description": image_description[:100],
                "user_requirements": user_requirements[:100]
            })
            raise
    
    async def _call_llm_for_planning(self, prompt: str) -> str:
        """调用LLM进行规划"""
        try:
            response = await self.llm_client.chat.completions.create(
                model=config.api.anthropic_model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=config.api.anthropic_max_tokens,
                temperature=config.api.anthropic_temperature
            )
            return response.choices[0].message.content
        except Exception as e:
            self.logger.error(f"LLM调用失败: {str(e)}")
            raise
    
    def _parse_plan_response(self, response: str) -> ExecutionPlan:
        """解析计划响应"""
        plan_id = f"plan_{int(datetime.now().timestamp())}"

        # 尝试从LLM响应中解析计划
        # 如果解析失败，使用默认计划
        try:
            # 尝试解析JSON格式的响应
            if "{" in response and "}" in response:
                import json
                # 提取JSON部分
                start = response.find("{")
                end = response.rfind("}") + 1
                json_str = response[start:end]
                parsed_plan = json.loads(json_str)

                # 如果成功解析，使用解析的计划
                if "steps" in parsed_plan:
                    return self._build_plan_from_parsed_data(plan_id, parsed_plan)
        except Exception as e:
            self.logger.warning(f"LLM响应解析失败，使用默认计划: {str(e)}")

        # 分析响应文本中的关键词来调整计划
        response_lower = response.lower()
        detail_level = "ultra_high" if "详细" in response or "深入" in response else "high"
        search_strategies = ["reverse_image", "text_search"]

        if "地标" in response or "landmark" in response_lower:
            search_strategies.append("landmark_search")
        if "文化" in response or "culture" in response_lower:
            search_strategies.append("cultural_search")
        
        # 创建标准的执行步骤
        steps = [
            PlanStep(
                step_id="step_1",
                stage=PlanStage.CLUE_EXTRACTION,
                actor_type="image_analysis",
                description="分析图片提取地理线索",
                priority=1,
                dependencies=[],
                expected_confidence=0.7,
                parameters={"detail_level": detail_level}
            ),
            PlanStep(
                step_id="step_2",
                stage=PlanStage.INVESTIGATION,
                actor_type="search",
                description="基于线索进行信息搜索",
                priority=2,
                dependencies=["step_1"],
                expected_confidence=0.8,
                parameters={"search_strategies": search_strategies}
            ),
            PlanStep(
                step_id="step_3",
                stage=PlanStage.REFLECTION,
                actor_type="reflection",
                description="验证和反思分析结果",
                priority=3,
                dependencies=["step_1", "step_2"],
                expected_confidence=0.85,
                parameters={"verification_rounds": 2}
            ),
            PlanStep(
                step_id="step_4",
                stage=PlanStage.CONCLUSION,
                actor_type="conclusion",
                description="整合信息得出最终结论",
                priority=4,
                dependencies=["step_1", "step_2", "step_3"],
                expected_confidence=0.9,
                parameters={"require_evidence": True}
            )
        ]
        
        return ExecutionPlan(
            plan_id=plan_id,
            steps=steps,
            estimated_duration=300.0,  # 5分钟
            success_criteria={
                "min_confidence": config.agent.confidence_threshold,
                "required_evidence_types": ["visual", "textual", "geographical"]
            },
            fallback_strategies=[
                "降低置信度要求",
                "增加搜索策略",
                "人工辅助验证"
            ]
        )
    
    def adjust_plan(
        self,
        current_plan: ExecutionPlan,
        execution_results: List[Dict[str, Any]],
        context: Dict[str, Any]
    ) -> ExecutionPlan:
        """根据执行结果调整计划"""
        
        self.logger.info("开始调整执行计划", 
                        plan_id=current_plan.plan_id,
                        results_count=len(execution_results))
        
        # 分析当前执行情况
        completed_steps = [r for r in execution_results if r.get("status") == "completed"]
        failed_steps = [r for r in execution_results if r.get("status") == "failed"]
        
        # 如果有失败的步骤，添加重试或替代步骤
        if failed_steps:
            self._add_fallback_steps(current_plan, failed_steps)
        
        # 如果置信度不够，添加额外的验证步骤
        current_confidence = context.get("current_confidence", 0.0)
        if current_confidence < config.agent.confidence_threshold:
            self._add_confidence_boosting_steps(current_plan, current_confidence)
        
        self.logger.info("执行计划调整完成", 
                        plan_id=current_plan.plan_id,
                        new_steps_count=len(current_plan.steps))
        
        return current_plan
    
    def _add_fallback_steps(self, plan: ExecutionPlan, failed_steps: List[Dict[str, Any]]) -> None:
        """添加失败步骤的回退策略"""
        for failed_step in failed_steps:
            step_type = failed_step.get("actor_type")
            
            if step_type == "search":
                # 添加替代搜索策略
                fallback_step = PlanStep(
                    step_id=f"fallback_search_{len(plan.steps)}",
                    stage=PlanStage.INVESTIGATION,
                    actor_type="search",
                    description="使用替代搜索策略",
                    priority=len(plan.steps) + 1,
                    dependencies=[],
                    expected_confidence=0.6,
                    parameters={"search_strategies": ["landmark_search", "cultural_search"]}
                )
                plan.steps.append(fallback_step)
    
    def _add_confidence_boosting_steps(self, plan: ExecutionPlan, current_confidence: float) -> None:
        """添加提升置信度的步骤"""
        if current_confidence < 0.5:
            # 添加额外的图片分析步骤
            boost_step = PlanStep(
                step_id=f"boost_analysis_{len(plan.steps)}",
                stage=PlanStage.CLUE_EXTRACTION,
                actor_type="image_analysis",
                description="深度图片分析提升置信度",
                priority=len(plan.steps) + 1,
                dependencies=[],
                expected_confidence=0.8,
                parameters={"detail_level": "ultra_high", "focus_areas": ["text", "landmarks"]}
            )
            plan.steps.append(boost_step)

    def _build_plan_from_parsed_data(self, plan_id: str, parsed_data: Dict[str, Any]) -> ExecutionPlan:
        """从解析的数据构建执行计划"""
        try:
            steps = []
            for i, step_data in enumerate(parsed_data.get("steps", [])):
                step = PlanStep(
                    step_id=step_data.get("step_id", f"step_{i+1}"),
                    stage=PlanStage(step_data.get("stage", "clue_extraction")),
                    actor_type=step_data.get("actor_type", "image_analysis"),
                    description=step_data.get("description", ""),
                    priority=step_data.get("priority", i+1),
                    dependencies=step_data.get("dependencies", []),
                    expected_confidence=step_data.get("expected_confidence", 0.7),
                    parameters=step_data.get("parameters", {})
                )
                steps.append(step)

            return ExecutionPlan(
                plan_id=plan_id,
                steps=steps,
                estimated_duration=parsed_data.get("estimated_duration", 300.0),
                success_criteria=parsed_data.get("success_criteria", {
                    "min_confidence": config.agent.confidence_threshold,
                    "required_evidence_types": ["visual", "textual", "geographical"]
                }),
                fallback_strategies=parsed_data.get("fallback_strategies", [
                    "降低置信度要求",
                    "增加搜索策略",
                    "人工辅助验证"
                ])
            )

        except Exception as e:
            self.logger.error(f"构建计划失败: {str(e)}")
            # 返回默认计划
            return self._create_default_plan(plan_id)

    def _create_default_plan(self, plan_id: str) -> ExecutionPlan:
        """创建默认执行计划"""
        steps = [
            PlanStep(
                step_id="step_1",
                stage=PlanStage.CLUE_EXTRACTION,
                actor_type="image_analysis",
                description="分析图片提取地理线索",
                priority=1,
                dependencies=[],
                expected_confidence=0.7,
                parameters={"detail_level": "high"}
            ),
            PlanStep(
                step_id="step_2",
                stage=PlanStage.INVESTIGATION,
                actor_type="search",
                description="基于线索进行信息搜索",
                priority=2,
                dependencies=["step_1"],
                expected_confidence=0.8,
                parameters={"search_strategies": ["reverse_image", "text_search"]}
            ),
            PlanStep(
                step_id="step_3",
                stage=PlanStage.REFLECTION,
                actor_type="reflection",
                description="验证和反思分析结果",
                priority=3,
                dependencies=["step_1", "step_2"],
                expected_confidence=0.85,
                parameters={"verification_rounds": 2}
            ),
            PlanStep(
                step_id="step_4",
                stage=PlanStage.CONCLUSION,
                actor_type="conclusion",
                description="整合信息得出最终结论",
                priority=4,
                dependencies=["step_1", "step_2", "step_3"],
                expected_confidence=0.9,
                parameters={"require_evidence": True}
            )
        ]

        return ExecutionPlan(
            plan_id=plan_id,
            steps=steps,
            estimated_duration=300.0,
            success_criteria={
                "min_confidence": config.agent.confidence_threshold,
                "required_evidence_types": ["visual", "textual", "geographical"]
            },
            fallback_strategies=[
                "降低置信度要求",
                "增加搜索策略",
                "人工辅助验证"
            ]
        )
