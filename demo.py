"""
图片地理位置识别Agent系统演示脚本
展示系统的主要功能和使用方法
"""

import asyncio
import json
import sys
from pathlib import Path
from typing import Dict, Any

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.agent import ImageLocationAgent
from src.utils import app_logger


class AgentDemo:
    """Agent系统演示类"""
    
    def __init__(self):
        self.logger = app_logger
        self.agent = None
    
    async def initialize_demo(self):
        """初始化演示环境"""
        print("🚀 初始化图片地理位置识别Agent系统...")

        try:
            # 创建真实的LLM客户端
            llm_client = await self._create_real_llm_client()

            # 创建Agent实例
            self.agent = ImageLocationAgent(llm_client)

            # 健康检查
            health_status = await self.agent.health_check()
            print(f"✅ Agent系统初始化完成")
            print(f"   - Agent状态: {health_status['agent_status']}")
            print(f"   - LLM客户端: {'已连接' if health_status['llm_client_available'] else '未连接'}")
            print(f"   - Actor数量: {len(health_status['actors_status'])}")

            return True

        except Exception as e:
            print(f"❌ 初始化失败: {str(e)}")
            print("请确保已正确配置API密钥")
            return False
    
    async def _create_real_llm_client(self):
        """创建真实的LLM客户端"""
        from src.utils import config
        import os

        # 检查API密钥配置
        openai_key = config.api.openai_api_key or os.getenv("OPENAI_API_KEY")
        anthropic_key = config.api.anthropic_api_key or os.getenv("ANTHROPIC_API_KEY")

        if openai_key:
            try:
                import openai
                client = openai.AsyncOpenAI(api_key=openai_key)
                print("✅ 使用OpenAI客户端")
                return client
            except ImportError:
                print("❌ OpenAI库未安装，请运行: pip install openai")
                raise
        elif anthropic_key:
            try:
                import anthropic
                client = anthropic.AsyncAnthropic(api_key=anthropic_key)
                print("✅ 使用Anthropic客户端")
                return client
            except ImportError:
                print("❌ Anthropic库未安装，请运行: pip install anthropic")
                raise
        else:
            raise ValueError("未找到有效的API密钥。请设置OPENAI_API_KEY或ANTHROPIC_API_KEY环境变量")
    
    async def demo_basic_analysis(self):
        """演示基本分析功能"""
        print("\n📸 演示基本图片分析功能")
        print("=" * 50)

        if not self.agent:
            print("❌ Agent未初始化，请先运行initialize_demo()")
            return

        # 使用真实的测试图片
        demo_image_path = "test.jpeg"

        if not Path(demo_image_path).exists():
            print(f"❌ 测试图片不存在: {demo_image_path}")
            print("请确保项目根目录下有test.jpeg文件")
            return

        try:
            print(f"🔍 分析图片: {demo_image_path}")

            # 使用真实的Agent进行分析
            result = await self.agent.analyze_image_location(
                image_path=demo_image_path,
                user_requirements="识别图片拍摄地点"
            )

            # 显示结果
            self._display_analysis_result(result)

        except Exception as e:
            print(f"❌ 分析失败: {str(e)}")
            print("这可能是由于API配置问题或网络连接问题")
    
    def _create_demo_image(self, image_path: str):
        """创建演示用的模拟图片文件"""
        with open(image_path, 'wb') as f:
            # 写入一些模拟的图片数据
            f.write(b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82')
    
    async def _simulate_analysis(self, image_path: str) -> Dict[str, Any]:
        """模拟分析过程"""
        print("   🔄 第1阶段: 图片特征提取...")
        await asyncio.sleep(1)  # 模拟处理时间
        
        print("   🔄 第2阶段: 信息搜索验证...")
        await asyncio.sleep(1)
        
        print("   🔄 第3阶段: 反思验证...")
        await asyncio.sleep(0.5)
        
        print("   🔄 第4阶段: 结论整合...")
        await asyncio.sleep(0.5)
        
        # 返回模拟的分析结果
        return {
            "success": True,
            "final_location": {
                "country": "中国",
                "region": "华北地区",
                "city": "北京",
                "specific_location": "天安门广场",
                "coordinates": {
                    "lat": 39.9042,
                    "lng": 116.4074
                }
            },
            "confidence_score": 0.87,
            "reasoning_process": """
1. 图片文字分析: 识别出3个文字线索，包括: 天安门, 北京, 中国
2. 地标识别: 发现1个可能的地标，包括: 天安门
3. 环境特征分析: vegetation: urban, climate: temperate, weather: sunny
4. 搜索验证: 通过多种搜索策略找到5个位置候选
5. 一致性验证: 各项证据相互支持，具有良好的一致性
6. 最终判断: 基于综合分析，确定最可能的位置为 天安门广场
            """.strip(),
            "key_evidence": [
                "文字线索: 天安门, 北京, 中国",
                "地标识别: 天安门",
                "搜索验证: 找到5个位置候选"
            ],
            "uncertainty_factors": [
                "图片分辨率中等",
                "部分建筑细节不够清晰"
            ],
            "alternative_possibilities": [
                "天安门城楼 (置信度: 0.82)",
                "故宫博物院 (置信度: 0.65)"
            ],
            "execution_details": {
                "total_iterations": 1,
                "execution_time": 3.2,
                "successful_tasks": 4,
                "failed_tasks": 0
            },
            "session_id": "demo_session_001"
        }
    
    def _display_analysis_result(self, result: Dict[str, Any]):
        """显示分析结果"""
        print("\n📋 分析结果")
        print("-" * 30)
        
        if result.get("success", False):
            location = result.get("final_location", {})
            print(f"🎯 识别结果: {location.get('specific_location', '未确定')}")
            print(f"📍 详细位置: {location.get('country', '')}, {location.get('city', '')}")
            
            coords = location.get("coordinates", {})
            if coords.get("lat"):
                print(f"🌐 坐标: ({coords['lat']:.4f}, {coords['lng']:.4f})")
            
            confidence = result.get("confidence_score", 0.0)
            print(f"🎲 置信度: {confidence:.2f} ({self._get_confidence_level(confidence)})")
            
            # 关键证据
            key_evidence = result.get("key_evidence", [])
            if key_evidence:
                print(f"\n🔍 关键证据:")
                for evidence in key_evidence:
                    print(f"   • {evidence}")
            
            # 执行统计
            execution = result.get("execution_details", {})
            print(f"\n⚡ 执行统计:")
            print(f"   • 执行时间: {execution.get('execution_time', 0):.1f}秒")
            print(f"   • 成功任务: {execution.get('successful_tasks', 0)}")
            print(f"   • 失败任务: {execution.get('failed_tasks', 0)}")
            
            # 不确定因素
            uncertainty = result.get("uncertainty_factors", [])
            if uncertainty:
                print(f"\n⚠️  不确定因素:")
                for factor in uncertainty:
                    print(f"   • {factor}")
            
            # 替代可能性
            alternatives = result.get("alternative_possibilities", [])
            if alternatives:
                print(f"\n🔄 替代可能性:")
                for alt in alternatives:
                    print(f"   • {alt}")
        
        else:
            print(f"❌ 分析失败: {result.get('error', '未知错误')}")
    
    def _get_confidence_level(self, confidence: float) -> str:
        """获取置信度等级描述"""
        if confidence >= 0.9:
            return "极高"
        elif confidence >= 0.8:
            return "高"
        elif confidence >= 0.6:
            return "中等"
        elif confidence >= 0.4:
            return "较低"
        else:
            return "低"
    
    async def demo_actor_capabilities(self):
        """演示各个Actor的能力"""
        print("\n🎭 演示Actor专业化能力")
        print("=" * 50)
        
        actors_info = {
            "ImageAnalysisActor": {
                "name": "图片分析专家",
                "capabilities": [
                    "OCR文字识别",
                    "建筑特征分析", 
                    "自然环境识别",
                    "地标检测"
                ]
            },
            "SearchActor": {
                "name": "信息搜索专家",
                "capabilities": [
                    "以图搜图",
                    "文本搜索验证",
                    "地理位置搜索",
                    "结果验证过滤"
                ]
            },
            "ReflectionActor": {
                "name": "反思验证专家", 
                "capabilities": [
                    "一致性检查",
                    "证据充分性评估",
                    "疑点识别",
                    "替代假设生成"
                ]
            },
            "ConclusionActor": {
                "name": "结论整合专家",
                "capabilities": [
                    "证据整合",
                    "位置确定",
                    "置信度评估",
                    "推理过程构建"
                ]
            }
        }
        
        for actor_type, info in actors_info.items():
            print(f"\n🤖 {info['name']} ({actor_type})")
            print(f"   专业能力:")
            for capability in info['capabilities']:
                print(f"   • {capability}")
    
    async def demo_knowledge_base(self):
        """演示知识库功能"""
        print("\n📚 演示知识库功能")
        print("=" * 50)
        
        print("🌍 地理知识库:")
        print("   • 气候区域分类 (热带、温带、干旱等)")
        print("   • 建筑风格识别 (中式传统、欧式古典、现代玻璃等)")
        print("   • 文化指标分析 (语言文字、交通模式等)")
        print("   • 地标分类管理 (宗教、历史、现代、自然等)")
        
        print("\n🏛️ 地标数据库:")
        print("   • 世界著名地标信息")
        print("   • 关键词搜索匹配")
        print("   • 地理位置搜索")
        print("   • 视觉特征匹配")
        
        # 演示知识库查询
        print("\n🔍 知识库查询示例:")
        print("   查询: '中式建筑特征'")
        print("   结果: 飞檐翘角、红墙黄瓦、斗拱结构、庭院布局")
        
        print("\n   查询: '埃菲尔铁塔'")
        print("   结果: 法国巴黎, 坐标(48.8584, 2.2945), 铁制塔状结构")
    
    async def run_demo(self):
        """运行完整演示"""
        print("🎉 欢迎使用图片地理位置识别Agent系统演示")
        print("=" * 60)
        
        # 初始化
        if not await self.initialize_demo():
            return
        
        # 基本分析演示
        await self.demo_basic_analysis()
        
        # Actor能力演示
        await self.demo_actor_capabilities()
        
        # 知识库演示
        await self.demo_knowledge_base()
        
        print("\n🎊 演示完成！")
        print("\n💡 使用提示:")
        print("   • 运行 'python main.py analyze image.jpg' 分析真实图片")
        print("   • 运行 'python main.py health' 检查系统状态")
        print("   • 查看 README.md 了解详细使用方法")
        print("   • 配置 .env 文件以使用真实的API服务")


async def main():
    """主函数"""
    demo = AgentDemo()
    await demo.run_demo()


if __name__ == "__main__":
    asyncio.run(main())
