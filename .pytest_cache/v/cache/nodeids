["tests/test_agent.py::TestContextManager::test_add_task_result", "tests/test_agent.py::TestContextManager::test_get_analysis_summary", "tests/test_agent.py::TestContextManager::test_initialization", "tests/test_agent.py::TestContextManager::test_initialize_context", "tests/test_agent.py::TestContextManager::test_should_continue_analysis", "tests/test_agent.py::TestImageAnalysisActor::test_actor_initialization", "tests/test_agent.py::TestImageAnalysisActor::test_execute_missing_input", "tests/test_agent.py::TestImageAnalysisActor::test_execute_success", "tests/test_agent.py::TestImageLocationAgent::test_actor_stats", "tests/test_agent.py::TestImageLocationAgent::test_agent_initialization", "tests/test_agent.py::TestImageLocationAgent::test_analyze_image_location_basic", "tests/test_agent.py::TestImageLocationAgent::test_analyze_image_location_error_handling", "tests/test_agent.py::TestImageLocationAgent::test_analyze_image_location_with_empty_requirements", "tests/test_agent.py::TestImageLocationAgent::test_analyze_image_location_with_large_image_path", "tests/test_agent.py::TestImageLocationAgent::test_context_management", "tests/test_agent.py::TestImageLocationAgent::test_health_check", "tests/test_resource_management.py::TestResourceManagement::test_cleanup_nonexistent_files", "tests/test_resource_management.py::TestResourceManagement::test_image_processor_cleanup_on_error", "tests/test_resource_management.py::TestResourceManagement::test_image_processor_destructor_cleanup", "tests/test_resource_management.py::TestResourceManagement::test_image_processor_temp_file_tracking", "tests/test_resource_management.py::TestResourceManagement::test_multiple_processors_independence", "tests/test_resource_management.py::TestResourceManagement::test_ocr_processor_destructor_cleanup", "tests/test_resource_management.py::TestResourceManagement::test_ocr_processor_temp_file_tracking", "tests/test_resource_management.py::TestResourceManagement::test_temp_file_cleanup_with_real_files"]