# 图片地理位置识别Agent系统配置

# API配置
api:
  openai:
    api_key: ${OPENAI_API_KEY}
    model: "gpt-4-vision-preview"
    max_tokens: 4000
    temperature: 0.1
  
  anthropic:
    api_key: ${ANTHROPIC_API_KEY}
    model: "claude-3-sonnet-20240229"
    max_tokens: 4000
    temperature: 0.1

# 工具配置
tools:
  image_processing:
    max_image_size: 2048
    supported_formats: ["jpg", "jpeg", "png", "webp", "bmp"]
    enhancement_enabled: true
  
  ocr:
    engines: ["tesseract", "easyocr"]
    languages: ["eng", "chi_sim", "chi_tra", "jpn", "kor"]
    confidence_threshold: 0.6
  
  search:
    google_api_key: ${GOOGLE_API_KEY}
    google_cse_id: ${GOOGLE_CSE_ID}
    bing_api_key: ${BING_API_KEY}
    max_results_per_query: 10
    timeout: 30

# Agent配置
agent:
  max_iterations: 5
  confidence_threshold: 0.8
  enable_reflection: true
  enable_multi_round_analysis: true

# Actor配置
actors:
  image_analysis:
    detail_level: "high"
    extract_objects: true
    extract_text: true
    extract_landmarks: true
    extract_architecture: true
    extract_nature: true
  
  search:
    search_strategies: ["reverse_image", "text_search", "landmark_search"]
    parallel_search: true
    max_concurrent_searches: 3
  
  reflection:
    verification_rounds: 2
    doubt_threshold: 0.3
    require_evidence: true

# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  file_path: "logs/agent.log"
  rotation: "1 day"
  retention: "30 days"

# 知识库配置
knowledge:
  landmark_db_path: "data/landmarks.json"
  geo_knowledge_path: "data/geo_knowledge.json"
  cache_enabled: true
  cache_ttl: 3600  # 1小时
