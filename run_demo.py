#!/usr/bin/env python3
"""
快速运行演示脚本
不需要安装额外依赖，展示系统架构和基本功能
"""

import asyncio
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))


async def simple_demo():
    """简单演示"""
    print("🎉 图片地理位置识别Agent系统")
    print("=" * 50)
    print("基于PlanAct框架的智能Agent系统")
    print("用于分析图片并准确推测拍摄地点")
    print()
    
    # 展示系统架构
    print("🏗️ 系统架构:")
    print("├── 🧠 核心框架 (PlanAct)")
    print("│   ├── 📋 Planner (规划器)")
    print("│   ├── 🎭 Actor Factory (执行单元工厂)")
    print("│   └── 📝 Context Manager (上下文管理)")
    print("│")
    print("├── 🎭 专业化Actor系统:")
    print("│   ├── 📸 ImageAnalysisActor (图片分析专家)")
    print("│   ├── 🔍 SearchActor (信息搜索专家)")
    print("│   ├── 🤔 ReflectionActor (反思验证专家)")
    print("│   └── 📊 ConclusionActor (结论整合专家)")
    print("│")
    print("├── 🛠️ 工具集成层:")
    print("│   ├── 🖼️ 图片处理工具 (增强、检测、分析)")
    print("│   ├── 📝 OCR工具 (文字识别)")
    print("│   └── 🌐 搜索工具 (以图搜图、文本搜索)")
    print("│")
    print("└── 📚 知识库系统:")
    print("    ├── 🌍 地理知识库 (气候、建筑、文化)")
    print("    └── 🏛️ 地标数据库 (世界著名地标)")
    print()
    
    # 展示工作流程
    print("🔄 核心工作流程:")
    print("1️⃣ 线索收集阶段 → 提取图片中的所有可用特征和线索")
    print("2️⃣ 调查分析阶段 → 逐一调查线索，整合信息形成初步结论")
    print("3️⃣ 审核反思阶段 → 验证结论，识别疑点，进行多轮分析优化")
    print("4️⃣ 结论输出阶段 → 给出最终地理位置判断和推理依据")
    print()
    
    # 模拟分析过程
    print("📸 模拟分析过程:")
    print("假设输入图片: 天安门广场.jpg")
    print()
    
    print("🔄 第1阶段: 线索收集")
    await asyncio.sleep(0.5)
    print("   ✅ OCR识别: '天安门', '北京', '中华人民共和国'")
    print("   ✅ 建筑特征: 传统中式建筑, 红墙黄瓦, 古典风格")
    print("   ✅ 地标检测: 天安门城楼 (置信度: 0.95)")
    print("   ✅ 环境分析: 城市环境, 温带气候, 晴朗天气")
    print()
    
    print("🔄 第2阶段: 调查分析")
    await asyncio.sleep(0.5)
    print("   ✅ 文本搜索: '天安门 北京' → 找到15个相关结果")
    print("   ✅ 以图搜图: 找到8个相似图片")
    print("   ✅ 地标搜索: 天安门广场 (39.9042°N, 116.4074°E)")
    print("   ✅ 位置验证: 北京市东城区天安门广场")
    print()
    
    print("🔄 第3阶段: 审核反思")
    await asyncio.sleep(0.5)
    print("   ✅ 一致性检查: 所有证据指向同一位置")
    print("   ✅ 证据充分性: 文字、视觉、地理证据齐全")
    print("   ✅ 疑点识别: 无明显疑点")
    print("   ✅ 置信度评估: 0.92 (高置信度)")
    print()
    
    print("🔄 第4阶段: 结论输出")
    await asyncio.sleep(0.5)
    print("   🎯 最终位置: 中国北京市天安门广场")
    print("   📍 精确坐标: (39.9042°N, 116.4074°E)")
    print("   🎲 置信度: 0.92 (高)")
    print("   📋 关键证据:")
    print("      • 文字线索: 天安门, 北京, 中华人民共和国")
    print("      • 地标识别: 天安门城楼")
    print("      • 搜索验证: 多个数据源确认")
    print()
    
    # 展示技术特点
    print("⭐ 技术特点:")
    print("• 🧠 智能规划: 动态调整分析策略")
    print("• 🎭 专业分工: 每个Actor专注特定领域")
    print("• 🔄 多轮迭代: 持续优化分析结果")
    print("• 🤔 自我反思: 主动识别和修正错误")
    print("• 📚 知识增强: 结合地理和文化知识")
    print("• 🛡️ 容错机制: 优雅处理各种异常情况")
    print()
    
    print("🚀 开始使用:")
    print("1. 配置API密钥 (复制 .env.example 为 .env)")
    print("2. 安装依赖: pip install -r requirements.txt")
    print("3. 分析图片: python main.py analyze your_image.jpg")
    print("4. 查看帮助: python main.py --help")
    print()
    
    print("📖 更多信息请查看 README.md")
    print("🎊 演示完成!")


def check_system_requirements():
    """检查系统要求"""
    print("🔍 检查系统要求...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version >= (3, 8):
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro}")
        print("   需要Python 3.8或更高版本")
        return False
    
    # 检查必要的模块
    required_modules = ['pathlib', 'asyncio', 'json', 'typing']
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ 模块 {module}: 可用")
        except ImportError:
            print(f"❌ 模块 {module}: 不可用")
            return False
    
    print("✅ 系统要求检查通过")
    return True


async def main():
    """主函数"""
    print("🎯 图片地理位置识别Agent系统演示")
    print("=" * 60)
    
    # 检查系统要求
    if not check_system_requirements():
        print("\n❌ 系统要求检查失败，请升级Python版本")
        return
    
    print()
    
    # 运行演示
    await simple_demo()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示运行出错: {str(e)}")
        print("请检查系统环境或联系开发者")
