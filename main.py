"""
图片地理位置识别Agent系统主入口
提供命令行接口和API服务
"""

import asyncio
import argparse
import json
import sys
from pathlib import Path
from typing import Optional

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.agent import ImageLocationAgent
from src.utils import config, app_logger


class ImageLocationCLI:
    """命令行接口"""
    
    def __init__(self):
        self.agent = None
        self.logger = app_logger
    
    async def initialize_agent(self, llm_provider: str = "openai") -> None:
        """初始化Agent"""
        try:
            # 根据提供商初始化LLM客户端
            llm_client = await self._create_llm_client(llm_provider)
            
            # 创建Agent实例
            self.agent = ImageLocationAgent(llm_client)
            
            # 健康检查
            health_status = await self.agent.health_check()
            self.logger.info("Agent初始化完成", health_status=health_status)
            
        except Exception as e:
            self.logger.error(f"Agent初始化失败: {str(e)}")
            raise
    
    async def _create_llm_client(self, provider: str):
        """创建LLM客户端"""
        if provider == "openai":
            try:
                import openai
                client = openai.AsyncOpenAI(
                    api_key=config.api.openai_api_key
                )
                return client
            except ImportError:
                self.logger.error("OpenAI库未安装，请运行: pip install openai")
                raise
            except Exception as e:
                self.logger.error(f"OpenAI客户端创建失败: {str(e)}")
                raise
        
        elif provider == "anthropic":
            try:
                import anthropic
                client = anthropic.AsyncAnthropic(
                    api_key=config.api.anthropic_api_key
                )
                return client
            except ImportError:
                self.logger.error("Anthropic库未安装，请运行: pip install anthropic")
                raise
            except Exception as e:
                self.logger.error(f"Anthropic客户端创建失败: {str(e)}")
                raise
        
        else:
            raise ValueError(f"不支持的LLM提供商: {provider}")
    
    async def analyze_image(
        self,
        image_path: str,
        output_path: Optional[str] = None,
        user_requirements: str = "识别图片拍摄地点",
        verbose: bool = False
    ) -> Dict[str, Any]:
        """分析图片地理位置"""
        
        if not self.agent:
            raise RuntimeError("Agent未初始化，请先调用initialize_agent()")
        
        # 检查图片文件是否存在
        if not Path(image_path).exists():
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
        
        self.logger.info(f"开始分析图片: {image_path}")
        
        try:
            # 执行分析
            result = await self.agent.analyze_image_location(
                image_path=image_path,
                user_requirements=user_requirements
            )
            
            # 输出结果
            if verbose:
                self._print_detailed_result(result)
            else:
                self._print_summary_result(result)
            
            # 保存结果到文件
            if output_path:
                await self._save_result_to_file(result, output_path)
            
            return result
            
        except Exception as e:
            self.logger.error(f"图片分析失败: {str(e)}")
            raise
    
    def _print_summary_result(self, result: Dict[str, Any]) -> None:
        """打印简要结果"""
        print("\n" + "="*60)
        print("图片地理位置分析结果")
        print("="*60)
        
        if result.get("success", False):
            location = result.get("final_location", {})
            confidence = result.get("confidence_score", 0.0)
            
            print(f"识别结果: {location.get('specific_location', '未确定')}")
            print(f"置信度: {confidence:.2f}")
            
            if location.get("coordinates", {}).get("lat"):
                coords = location["coordinates"]
                print(f"坐标: ({coords['lat']:.6f}, {coords['lng']:.6f})")
            
            key_evidence = result.get("key_evidence", [])
            if key_evidence:
                print(f"关键证据: {'; '.join(key_evidence[:3])}")
        else:
            print(f"分析失败: {result.get('error', '未知错误')}")
        
        print("="*60)
    
    def _print_detailed_result(self, result: Dict[str, Any]) -> None:
        """打印详细结果"""
        print("\n" + "="*80)
        print("详细分析结果")
        print("="*80)
        
        # 基本信息
        print(f"分析状态: {'成功' if result.get('success') else '失败'}")
        print(f"会话ID: {result.get('session_id', 'N/A')}")
        
        if result.get("success", False):
            # 位置信息
            location = result.get("final_location", {})
            print(f"\n位置信息:")
            print(f"  具体位置: {location.get('specific_location', '未确定')}")
            print(f"  国家: {location.get('country', '未确定')}")
            print(f"  地区: {location.get('region', '未确定')}")
            print(f"  城市: {location.get('city', '未确定')}")
            
            coords = location.get("coordinates", {})
            if coords.get("lat"):
                print(f"  坐标: ({coords['lat']:.6f}, {coords['lng']:.6f})")
            
            # 置信度信息
            confidence = result.get("confidence_score", 0.0)
            print(f"\n置信度: {confidence:.2f}")
            
            # 推理过程
            reasoning = result.get("reasoning_process", "")
            if reasoning:
                print(f"\n推理过程:")
                for line in reasoning.split('\n'):
                    if line.strip():
                        print(f"  {line}")
            
            # 关键证据
            key_evidence = result.get("key_evidence", [])
            if key_evidence:
                print(f"\n关键证据:")
                for evidence in key_evidence:
                    print(f"  - {evidence}")
            
            # 不确定因素
            uncertainty = result.get("uncertainty_factors", [])
            if uncertainty:
                print(f"\n不确定因素:")
                for factor in uncertainty:
                    print(f"  - {factor}")
            
            # 替代可能性
            alternatives = result.get("alternative_possibilities", [])
            if alternatives:
                print(f"\n替代可能性:")
                for alt in alternatives:
                    print(f"  - {alt}")
            
            # 执行统计
            execution = result.get("execution_details", {})
            print(f"\n执行统计:")
            print(f"  迭代次数: {execution.get('total_iterations', 0)}")
            print(f"  执行时间: {execution.get('execution_time', 0.0):.2f}秒")
            print(f"  成功任务: {execution.get('successful_tasks', 0)}")
            print(f"  失败任务: {execution.get('failed_tasks', 0)}")
        
        else:
            print(f"\n错误信息: {result.get('error', '未知错误')}")
        
        print("="*80)
    
    async def _save_result_to_file(self, result: Dict[str, Any], output_path: str) -> None:
        """保存结果到文件"""
        try:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"结果已保存到: {output_path}")
            
        except Exception as e:
            self.logger.error(f"保存结果失败: {str(e)}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="图片地理位置识别Agent系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py analyze image.jpg
  python main.py analyze image.jpg --output result.json --verbose
  python main.py analyze image.jpg --requirements "识别这是哪个城市" --provider anthropic
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 分析命令
    analyze_parser = subparsers.add_parser('analyze', help='分析图片地理位置')
    analyze_parser.add_argument('image_path', help='图片文件路径')
    analyze_parser.add_argument('--output', '-o', help='结果输出文件路径')
    analyze_parser.add_argument('--requirements', '-r', 
                               default='识别图片拍摄地点',
                               help='分析要求描述')
    analyze_parser.add_argument('--provider', '-p', 
                               choices=['openai', 'anthropic'],
                               default='openai',
                               help='LLM提供商')
    analyze_parser.add_argument('--verbose', '-v', 
                               action='store_true',
                               help='显示详细结果')
    
    # 健康检查命令
    health_parser = subparsers.add_parser('health', help='系统健康检查')
    health_parser.add_argument('--provider', '-p',
                              choices=['openai', 'anthropic'], 
                              default='openai',
                              help='LLM提供商')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 创建CLI实例
    cli = ImageLocationCLI()
    
    try:
        if args.command == 'analyze':
            # 初始化Agent
            await cli.initialize_agent(args.provider)
            
            # 执行分析
            await cli.analyze_image(
                image_path=args.image_path,
                output_path=args.output,
                user_requirements=args.requirements,
                verbose=args.verbose
            )
        
        elif args.command == 'health':
            # 初始化Agent
            await cli.initialize_agent(args.provider)
            
            # 执行健康检查
            health_status = await cli.agent.health_check()
            print("\n系统健康状态:")
            print(json.dumps(health_status, ensure_ascii=False, indent=2))
    
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n执行失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
